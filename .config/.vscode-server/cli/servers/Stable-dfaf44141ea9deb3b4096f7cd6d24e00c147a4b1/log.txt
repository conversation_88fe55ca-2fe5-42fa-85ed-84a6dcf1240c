*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[04:11:52] 




[04:11:52] Extension host agent started.
[04:11:53] [<unknown>][272a4168][ManagementConnection] Unknown reconnection token (never seen).
[04:12:01] [<unknown>][7c560750][ExtensionHostConnection] New connection established.
[04:12:01] [<unknown>][66fd4738][ManagementConnection] New connection established.
[04:12:01] [<unknown>][7c560750][ExtensionHostConnection] <14636> Launched Extension Host Process.
[04:12:01] ComputeTargetPlatform: linux-x64
[04:12:06] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[04:16:52] New EH opened, aborting shutdown
[04:26:57] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[09:03:22] [<unknown>][66fd4738][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[09:03:23] [<unknown>][7c560750][ExtensionHostConnection] <14636> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[09:03:23] Last EH closed, waiting before shutting down
[09:03:46] [<unknown>][4c3c3360][ManagementConnection] New connection established.
[09:03:46] [<unknown>][2f84f3fe][ExtensionHostConnection] New connection established.
[09:03:46] [<unknown>][2f84f3fe][ExtensionHostConnection] <27214> Launched Extension Host Process.
[09:03:51] Getting Manifest... github.copilot
[09:03:51] Installing extension: github.copilot {
  productVersion: { version: '1.101.0', date: '2025-06-11T15:00:50.123Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[09:03:53] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1212ms.
[09:03:54] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.335.0: github.copilot
[09:03:54] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.335.0
[09:03:54] Marked extension as removed github.copilot-1.333.0
[09:03:54] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[09:07:22] [<unknown>][4c3c3360][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[09:07:22] [<unknown>][2f84f3fe][ExtensionHostConnection] <27214> Extension Host Process exited with code: 0, signal: null.
Cancelling previous shutdown timeout
[09:07:22] Cancelling previous shutdown timeout
Last EH closed, waiting before shutting down
[09:07:22] Last EH closed, waiting before shutting down
[09:07:24] [<unknown>][ed5cc218][ManagementConnection] New connection established.
[09:07:24] [<unknown>][5c9d707e][ExtensionHostConnection] New connection established.
[09:07:24] [<unknown>][5c9d707e][ExtensionHostConnection] <27916> Launched Extension Host Process.
