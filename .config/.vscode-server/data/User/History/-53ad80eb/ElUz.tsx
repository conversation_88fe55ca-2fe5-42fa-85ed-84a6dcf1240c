import React, { useState } from 'react';
import { Zap, Mail, Phone, MapPin, ArrowUp, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { validateNewsletterEmail } from '../utils/validation';
import { subscribeToNewsletter } from '../utils/emailService';

const Footer: React.FC = () => {
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [newsletterMessage, setNewsletterMessage] = useState('');
  const [emailError, setEmailError] = useState('');

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous states
    setEmailError('');
    setNewsletterMessage('');

    // Validate email
    const validation = validateNewsletterEmail(newsletterEmail);
    if (!validation.isValid) {
      setEmailError(validation.errors.email || 'Please enter a valid email');
      return;
    }

    setNewsletterStatus('loading');

    try {
      const result = await subscribeToNewsletter({ email: newsletterEmail });

      if (result.success) {
        setNewsletterStatus('success');
        setNewsletterMessage(result.message);
        setNewsletterEmail('');

        // Reset success state after 5 seconds
        setTimeout(() => {
          setNewsletterStatus('idle');
          setNewsletterMessage('');
        }, 5000);
      } else {
        setNewsletterStatus('error');
        setNewsletterMessage(result.message);
      }
    } catch (error) {
      setNewsletterStatus('error');
      setNewsletterMessage('An unexpected error occurred. Please try again.');
    }
  };

  const quickLinks = [
    { name: 'About', href: '#about' },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Technology', href: '#tech' },
    { name: 'Contact', href: '#contact' }
  ];

  const services = [
    { name: 'Web Design', href: '#services' },
    { name: 'Web Development', href: '#services' },
    { name: 'Business Automation', href: '#services' },
    { name: 'AI Solutions', href: '#services' },
    { name: 'Small Business Essentials', href: '#services' },
    { name: 'Consulting', href: '#contact' }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16 grid lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-6">
              <div className="relative">
                <Zap className="h-8 w-8 text-purple-400" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                beam.tech
              </span>
            </div>
            <p className="text-gray-300 leading-relaxed mb-6">
              Transforming businesses through cutting-edge web design, automation, and AI solutions. 
              We beam your business to the future.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-3 text-gray-300">
                <Mail className="h-5 w-5 text-purple-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <Phone className="h-5 w-5 text-purple-400" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <MapPin className="h-5 w-5 text-purple-400" />
                <span>Margaret River and Canberra, Australia</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Quick Links</h3>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-purple-400 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Services</h3>
            <ul className="space-y-3">
              {services.map((service) => (
                <li key={service.name}>
                  <a
                    href={service.href}
                    className="text-gray-300 hover:text-purple-400 transition-colors duration-200"
                  >
                    {service.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Stay Updated</h3>
            <p className="text-gray-300 mb-4">
              Get the latest insights on web design, automation, and AI trends.
            </p>
            {newsletterStatus === 'success' ? (
              <div className="text-center py-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <p className="text-green-600 dark:text-green-400 text-sm">
                  {newsletterMessage}
                </p>
              </div>
            ) : (
              <form onSubmit={handleNewsletterSubmit} className="space-y-3">
                <div>
                  <input
                    type="email"
                    value={newsletterEmail}
                    onChange={(e) => {
                      setNewsletterEmail(e.target.value);
                      if (emailError) setEmailError('');
                    }}
                    placeholder="Enter your email"
                    className={`w-full px-4 py-3 bg-gray-800 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 ${
                      emailError ? 'border-red-500' : 'border-gray-700'
                    }`}
                    disabled={newsletterStatus === 'loading'}
                  />
                  {emailError && (
                    <p className="mt-1 text-sm text-red-400 flex items-center">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      {emailError}
                    </p>
                  )}
                </div>

                {newsletterMessage && newsletterStatus === 'error' && (
                  <p className="text-sm text-red-400 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    {newsletterMessage}
                  </p>
                )}

                <button
                  type="submit"
                  disabled={newsletterStatus === 'loading' || !newsletterEmail.trim()}
                  className={`w-full font-semibold py-3 rounded-lg transition-all duration-200 ${
                    newsletterStatus === 'loading' || !newsletterEmail.trim()
                      ? 'bg-gray-600 cursor-not-allowed'
                      : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-lg'
                  } text-white`}
                >
                  {newsletterStatus === 'loading' ? (
                    <span className="flex items-center justify-center">
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Subscribing...
                    </span>
                  ) : (
                    'Subscribe'
                  )}
                </button>
              </form>
            )}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © 2025 Beam.tech. All rights reserved. Made with ❤️ in Margaret River and Canberra, Australia.
            </div>
            
            <div className="flex items-center space-x-6">
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Terms of Service
              </a>
              <button
                onClick={scrollToTop}
                className="p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200"
                aria-label="Scroll to top"
              >
                <ArrowUp className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;