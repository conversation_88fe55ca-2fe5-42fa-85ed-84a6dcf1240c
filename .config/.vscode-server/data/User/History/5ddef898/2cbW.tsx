import React, { useState } from 'react';
import { Code, Bot, Zap, ArrowRight, CheckCircle, Settings } from 'lucide-react';
import { scrollToContact } from '../utils/navigation';
import QuickQuoteModal from './QuickQuoteModal';

const Services: React.FC = () => {
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);

  const services = [
    {
      id: 'web-design',
      title: 'Web Design & Development',
      description: 'Custom websites and web applications built with cutting-edge technologies',
      icon: Code,
      features: [
        'Responsive Design',
        'Performance Optimization',
        'SEO Integration',
        'Modern Frameworks',
        'Cross-browser Compatibility'
      ],
      color: 'from-purple-500 to-blue-500'
    },
    {
      id: 'automation',
      title: 'Business Automation',
      description: 'Streamline your workflows with intelligent automation solutions',
      icon: Zap,
      features: [
        'Process Automation',
        'Data Integration',
        'Workflow Optimization',
        'Custom APIs',
        'Third-party Integrations'
      ],
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'ai',
      title: 'AI Solutions',
      description: 'Harness the power of artificial intelligence for your business',
      icon: Bot,
      features: [
        'Machine Learning Models',
        'Natural Language Processing',
        'Computer Vision',
        'Predictive Analytics',
        'AI-powered Chatbots'
      ],
      color: 'from-purple-500 to-pink-500'
    }
  ];

  return (
    <section id="services" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Our <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">Services</span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            We deliver comprehensive digital solutions that drive growth and innovation
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {services.map((service) => {
            const IconComponent = service.icon;
            return (
              <div
                key={service.id}
                className="group relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700"
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>
                
                {/* Icon */}
                <div className={`inline-flex p-4 bg-gradient-to-r ${service.color} rounded-xl mb-6`}>
                  <IconComponent className="h-8 w-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <button
                  onClick={scrollToContact}
                  className="group/btn inline-flex items-center justify-center w-full px-6 py-3 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-semibold rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200"
                >
                  <span>Learn More</span>
                  <ArrowRight className="ml-2 h-5 w-5 group-hover/btn:translate-x-1 transition-transform duration-200" />
                </button>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
            Ready to transform your business? Let's discuss your project.
          </p>
          <button
            onClick={scrollToContact}
            className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
          >
            Get Started Today
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Services;