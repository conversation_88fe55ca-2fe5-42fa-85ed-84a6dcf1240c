// Form validation utilities

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface ContactFormData {
  name: string;
  email: string;
  company: string;
  project: string;
  budget: string;
  message: string;
}

export interface QuickQuoteFormData {
  service: string;
  serviceOther: string;
  websiteUrl: string;
  description: string;
  email: string;
  phone: string;
}

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateContactForm = (data: ContactFormData): ValidationResult => {
  const errors: Record<string, string> = {};

  // Name validation
  if (!data.name.trim()) {
    errors.name = 'Name is required';
  } else if (data.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters';
  }

  // Email validation
  if (!data.email.trim()) {
    errors.email = 'Email is required';
  } else if (!validateEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Company validation (optional but if provided, should be valid)
  if (data.company.trim() && data.company.trim().length < 2) {
    errors.company = 'Company name must be at least 2 characters';
  }

  // Project type validation
  if (!data.project) {
    errors.project = 'Please select a project type';
  }

  // Budget validation
  if (!data.budget) {
    errors.budget = 'Please select a budget range';
  }

  // Message validation
  if (!data.message.trim()) {
    errors.message = 'Message is required';
  } else if (data.message.trim().length < 10) {
    errors.message = 'Message must be at least 10 characters';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

export const validateNewsletterEmail = (email: string): ValidationResult => {
  const errors: Record<string, string> = {};

  if (!email.trim()) {
    errors.email = 'Email is required';
  } else if (!validateEmail(email)) {
    errors.email = 'Please enter a valid email address';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

export const validateQuickQuoteForm = (data: QuickQuoteFormData): ValidationResult => {
  const errors: Record<string, string> = {};

  // Service validation
  if (!data.service) {
    errors.service = 'Please select a service';
  } else if (data.service === 'other' && !data.serviceOther.trim()) {
    errors.serviceOther = 'Please describe what you need';
  } else if (data.service === 'other' && data.serviceOther.trim().length < 5) {
    errors.serviceOther = 'Please provide more details (at least 5 characters)';
  }

  // Website URL validation (optional but if provided, should be valid)
  if (data.websiteUrl.trim() && data.websiteUrl.trim() !== 'none') {
    const urlRegex = /^https?:\/\/.+\..+/;
    if (!urlRegex.test(data.websiteUrl)) {
      errors.websiteUrl = 'Please enter a valid URL (e.g., https://example.com)';
    }
  }

  // Description validation
  if (!data.description.trim()) {
    errors.description = 'Please tell us more about what you need';
  } else if (data.description.trim().length < 10) {
    errors.description = 'Please provide more details (at least 10 characters)';
  }

  // Email validation
  if (!data.email.trim()) {
    errors.email = 'Email is required';
  } else if (!validateEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Phone validation (optional but if provided, should be valid)
  if (data.phone.trim()) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
