import fs from 'fs';
import path from 'path';

// Simple text logging utility for contact forms and newsletter subscriptions

/**
 * Interface for contact form entries
 */
export interface ContactFormEntry {
  timestamp: string;
  name: string;
  email: string;
  company: string;
  project_type: string;
  budget: string;
  message: string;
  submission_status: 'success' | 'failed';
}

/**
 * Interface for newsletter entries
 */
export interface NewsletterEntry {
  timestamp: string;
  email: string;
  subscription_status: 'success' | 'failed';
}

/**
 * Interface for quick quote entries
 */
export interface QuickQuoteEntry {
  timestamp: string;
  service: string;
  service_other: string;
  website_url: string;
  description: string;
  email: string;
  phone: string;
  submission_status: 'success' | 'failed';
}

/**
 * Format contact form entry as text line
 */
function formatContactFormEntry(entry: ContactFormEntry): string {
  const cleanMessage = entry.message.replace(/\n/g, ' ').replace(/\r/g, ' ').substring(0, 200);
  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;
}

/**
 * Format newsletter entry as text line
 */
function formatNewsletterEntry(entry: NewsletterEntry): string {
  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;
}

/**
 * Format quick quote entry as text line
 */
function formatQuickQuoteEntry(entry: QuickQuoteEntry): string {
  const cleanDescription = entry.description.replace(/\n/g, ' ').replace(/\r/g, ' ').substring(0, 200);
  const serviceText = entry.service === 'other' ? `${entry.service} (${entry.service_other})` : entry.service;
  return `[${entry.timestamp}] QUOTE: ${entry.email} | Service: ${serviceText} | Website: ${entry.website_url || 'N/A'} | Phone: ${entry.phone || 'N/A'} | Description: ${cleanDescription} | Status: ${entry.submission_status}`;
}

/**
 * Get absolute log file path using direct path resolution
 */
function getLogFilePath(filename: string): string {
  // Use ES module compatible path resolution
  const serverDir = path.dirname(new URL(import.meta.url).pathname);
  const logDir = path.join(serverDir, 'logs');
  return path.join(logDir, filename);
}

/**
 * Ensure log directory exists
 */
async function ensureLogDirectory(): Promise<string> {
  const serverDir = path.dirname(new URL(import.meta.url).pathname);
  const logDir = path.join(serverDir, 'logs');
  
  console.log(`📁 Ensuring log directory exists: ${logDir}`);
  
  try {
    // Create directory if it doesn't exist
    await fs.promises.mkdir(logDir, { recursive: true });
    console.log(`✅ Log directory ready: ${logDir}`);
    return logDir;
  } catch (error) {
    console.error(`❌ Error creating log directory:`, error);
    throw error;
  }
}

/**
 * Initialize log files
 */
export async function ensureLogFiles(): Promise<void> {
  console.log('🔍 Initializing log files...');
  
  try {
    // Ensure log directory exists
    const logDir = await ensureLogDirectory();
    
    // Define file paths
    const contactFile = path.join(logDir, 'contact-form-submissions.txt');
    const newsletterFile = path.join(logDir, 'newsletter-subscriptions.txt');
    const quickQuoteFile = path.join(logDir, 'quick-quote-submissions.txt');

    console.log(`📄 Contact log file: ${contactFile}`);
    console.log(`📄 Newsletter log file: ${newsletterFile}`);
    console.log(`📄 Quick Quote log file: ${quickQuoteFile}`);
    
    // Touch files to create them if they don't exist
    for (const file of [contactFile, newsletterFile]) {
      try {
        // Check if file exists, create if not
        if (!fs.existsSync(file)) {
          console.log(`� Creating log file: ${file}`);
          await fs.promises.writeFile(file, '', 'utf8');
          console.log(`✅ Created log file: ${file}`);
        } else {
          console.log(`✅ Log file already exists: ${file}`);
        }
      } catch (fileError) {
        console.error(`❌ Error with log file ${file}:`, fileError);
      }
    }
    
    console.log('✅ Log files initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize log files:', error);
  }
}

/**
 * Append entry to text file
 */
async function appendToFile(filePath: string, content: string): Promise<void> {
  console.log(`� Appending to file: ${filePath}`);
  
  try {
    // Ensure directory exists
    await ensureLogDirectory();
    
    // Append to file with newline
    await fs.promises.appendFile(filePath, content + '\n', 'utf8');
    console.log(`✅ Successfully appended to file: ${filePath}`);
  } catch (error) {
    console.error(`❌ Error appending to file:`, error);
    throw error;
  }
}

/**
 * Log contact form submission
 */
export async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {
  console.log(`� Logging contact form submission for: ${entry.email}`);
  
  try {
    // Get log file path
    const filePath = getLogFilePath('contact-form-submissions.txt');
    console.log(`� Using log file: ${filePath}`);
    
    // Format entry as text
    const textLine = formatContactFormEntry(entry);
    console.log(`🔍 Formatted log entry: ${textLine}`);
    
    // Write to file
    await appendToFile(filePath, textLine);
    console.log(`✅ Contact form submission logged successfully: ${entry.email}`);
  } catch (error) {
    console.error(`❌ Failed to log contact form submission:`, error);
    // Do not throw to prevent breaking application flow
  }
}

/**
 * Log newsletter subscription
 */
export async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {
  console.log(`� Logging newsletter subscription for: ${entry.email}`);

  try {
    // Get log file path
    const filePath = getLogFilePath('newsletter-subscriptions.txt');
    console.log(`� Using log file: ${filePath}`);

    // Format entry as text
    const textLine = formatNewsletterEntry(entry);
    console.log(`🔍 Formatted log entry: ${textLine}`);

    // Write to file
    await appendToFile(filePath, textLine);
    console.log(`✅ Newsletter subscription logged successfully: ${entry.email}`);
  } catch (error) {
    console.error(`❌ Failed to log newsletter subscription:`, error);
    // Do not throw to prevent breaking application flow
  }
}

/**
 * Log quick quote submission
 */
export async function logQuickQuoteSubmission(entry: QuickQuoteEntry): Promise<void> {
  console.log(`📝 Logging quick quote submission for: ${entry.email}`);

  try {
    // Get log file path
    const filePath = getLogFilePath('quick-quote-submissions.txt');
    console.log(`📁 Using log file: ${filePath}`);

    // Format entry as text
    const textLine = formatQuickQuoteEntry(entry);
    console.log(`🔍 Formatted log entry: ${textLine}`);

    // Write to file
    await appendToFile(filePath, textLine);
    console.log(`✅ Quick quote submission logged successfully: ${entry.email}`);
  } catch (error) {
    console.error(`❌ Failed to log quick quote submission:`, error);
    // Do not throw to prevent breaking application flow
  }
}

/**
 * Get current ISO timestamp
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Create contact form entry
 */
export function createContactFormEntry(
  name: string,
  email: string,
  company: string,
  project: string,
  budget: string,
  message: string,
  status: 'success' | 'failed'
): ContactFormEntry {
  console.log(`🔍 Creating contact form entry for: ${email} (${status})`);
  return {
    timestamp: getCurrentTimestamp(),
    name: name || '',
    email: email || '',
    company: company || '',
    project_type: project || '',
    budget: budget || '',
    message: message || '',
    submission_status: status
  };
}

/**
 * Create newsletter entry
 */
export function createNewsletterEntry(
  email: string,
  status: 'success' | 'failed'
): NewsletterEntry {
  console.log(`🔍 Creating newsletter entry for: ${email} (${status})`);
  return {
    timestamp: getCurrentTimestamp(),
    email: email || '',
    subscription_status: status
  };
}
