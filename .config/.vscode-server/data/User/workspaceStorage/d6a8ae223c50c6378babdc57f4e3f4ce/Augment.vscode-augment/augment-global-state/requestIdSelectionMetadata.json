[["29e6764f-4ab3-4593-91aa-22fba5742024", {"value": {"selectedCode": "", "prefix": "import React from 'react';\nimport { Zap, Mail, Phone, MapPin, ArrowUp } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const services = [\n    { name: 'Web Design', href: '#services' },\n    { name: 'Web Development', href: '#services' },\n    { name: 'Business Automation', href: '#services' },\n    { name: 'AI Solutions', href: '#services' },\n    { name: 'Consulting', href: '#contact' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16 grid lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"relative\">\n                <Zap className=\"h-8 w-8 text-purple-400\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                beam.tech\n              </span>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6\">\n              Transforming businesses through cutting-edge web design, automation, and AI solutions. \n              We beam your business to the future.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Mail className=\"h-5 w-5 text-purple-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Phone className=\"h-5 w-5 text-purple-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <MapPin className=\"h-5 w-5 text-purple-400\" />\n                <span>Margaret River and Canberra, Australia</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Stay Updated</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Get the latest insights on web design, automation, and AI trends.\n            </p>\n            <div className=\"space-y-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 rounded-lg hover:shadow-lg transition-all duration-200\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n", "suffix": "\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2025 Beam.tech. All rights reserved. Made with ❤️ in Margaret River and Canberra, Australia.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;", "path": "client/src/components/Footer.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 22}}], ["51c3398c-bad2-4306-b1a5-bb2c92bc86ab", {"value": {"selectedCode": "", "prefix": "import React, { useState } from 'react';\nimport { Zap, Mail, Phone, MapPin, ArrowUp, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport { validateNewsletterEmail } from '../utils/validation';\nimport { subscribeToNewsletter } from '../utils/emailService';\n\nconst Footer: React.FC = () => {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const services = [\n    { name: 'Web Design', href: '#services' },\n    { name: 'Web Development', href: '#services' },\n    { name: 'Business Automation', href: '#services' },\n    { name: 'AI Solutions', href: '#services' },\n    { name: 'Consulting', href: '#contact' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16 grid lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"relative\">\n                <Zap className=\"h-8 w-8 text-purple-400\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                beam.tech\n              </span>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6\">\n              Transforming businesses through cutting-edge web design, automation, and AI solutions. \n              We beam your business to the future.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Mail className=\"h-5 w-5 text-purple-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Phone className=\"h-5 w-5 text-purple-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <MapPin className=\"h-5 w-5 text-purple-400\" />\n                <span>Margaret River and Canberra, Australia</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Stay Updated</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Get the latest insights on web design, automation, and AI trends.\n            </p>\n            <div className=\"space-y-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 rounded-lg hover:shadow-lg transition-all duration-200\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n", "suffix": "\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2025 Beam.tech. All rights reserved. Made with ❤️ in Margaret River and Canberra, Australia.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;", "path": "client/src/components/Footer.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 22}}], ["928bc64a-179a-48fa-9862-27557db40555", {"value": {"selectedCode": "", "prefix": "import React, { useState } from 'react';\nimport { Zap, Mail, Phone, MapPin, ArrowUp, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport { validateNewsletterEmail } from '../utils/validation';\nimport { subscribeToNewsletter } from '../utils/emailService';\n\nconst Footer: React.FC = () => {\n  const [newsletterEmail, setNewsletterEmail] = useState('');\n  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const [newsletterMessage, setNewsletterMessage] = useState('');\n  const [emailError, setEmailError] = useState('');\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const handleNewsletterSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Clear previous states\n    setEmailError('');\n    setNewsletterMessage('');\n\n    // Validate email\n    const validation = validateNewsletterEmail(newsletterEmail);\n    if (!validation.isValid) {\n      setEmailError(validation.errors.email || 'Please enter a valid email');\n      return;\n    }\n\n    setNewsletterStatus('loading');\n\n    try {\n      const result = await subscribeToNewsletter({ email: newsletterEmail });\n\n      if (result.success) {\n        setNewsletterStatus('success');\n        setNewsletterMessage(result.message);\n        setNewsletterEmail('');\n\n        // Reset success state after 5 seconds\n        setTimeout(() => {\n          setNewsletterStatus('idle');\n          setNewsletterMessage('');\n        }, 5000);\n      } else {\n        setNewsletterStatus('error');\n        setNewsletterMessage(result.message);\n      }\n    } catch (error) {\n      setNewsletterStatus('error');\n      setNewsletterMessage('An unexpected error occurred. Please try again.');\n    }\n  };\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const services = [\n    { name: 'Web Design', href: '#services' },\n    { name: 'Web Development', href: '#services' },\n    { name: 'Business Automation', href: '#services' },\n    { name: 'AI Solutions', href: '#services' },\n    { name: 'Consulting', href: '#contact' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16 grid lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"relative\">\n                <Zap className=\"h-8 w-8 text-purple-400\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                beam.tech\n              </span>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6\">\n              Transforming businesses through cutting-edge web design, automation, and AI solutions. \n              We beam your business to the future.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Mail className=\"h-5 w-5 text-purple-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Phone className=\"h-5 w-5 text-purple-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <MapPin className=\"h-5 w-5 text-purple-400\" />\n                <span>Margaret River and Canberra, Australia</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Stay Updated</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Get the latest insights on web design, automation, and AI trends.\n            </p>\n            <div className=\"space-y-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400\"\n              />\n              <button className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold py-3 rounded-lg hover:shadow-lg transition-all duration-200\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n", "suffix": "\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2025 Beam.tech. All rights reserved. Made with ❤️ in Margaret River and Canberra, Australia.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;", "path": "client/src/components/Footer.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 22}}], ["084ffafa-d641-4aa3-b5f5-b651fab95496", {"value": {"selectedCode": "", "prefix": "import React, { useState } from 'react';\nimport { Zap, Mail, Phone, MapPin, ArrowUp, Loader2, CheckCircle, AlertCircle } from 'lucide-react';\nimport { validateNewsletterEmail } from '../utils/validation';\nimport { subscribeToNewsletter } from '../utils/emailService';\n\nconst Footer: React.FC = () => {\n  const [newsletterEmail, setNewsletterEmail] = useState('');\n  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const [newsletterMessage, setNewsletterMessage] = useState('');\n  const [emailError, setEmailError] = useState('');\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const handleNewsletterSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Clear previous states\n    setEmailError('');\n    setNewsletterMessage('');\n\n    // Validate email\n    const validation = validateNewsletterEmail(newsletterEmail);\n    if (!validation.isValid) {\n      setEmailError(validation.errors.email || 'Please enter a valid email');\n      return;\n    }\n\n    setNewsletterStatus('loading');\n\n    try {\n      const result = await subscribeToNewsletter({ email: newsletterEmail });\n\n      if (result.success) {\n        setNewsletterStatus('success');\n        setNewsletterMessage(result.message);\n        setNewsletterEmail('');\n\n        // Reset success state after 5 seconds\n        setTimeout(() => {\n          setNewsletterStatus('idle');\n          setNewsletterMessage('');\n        }, 5000);\n      } else {\n        setNewsletterStatus('error');\n        setNewsletterMessage(result.message);\n      }\n    } catch (error) {\n      setNewsletterStatus('error');\n      setNewsletterMessage('An unexpected error occurred. Please try again.');\n    }\n  };\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Services', href: '#services' },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Technology', href: '#tech' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const services = [\n    { name: 'Web Design', href: '#services' },\n    { name: 'Web Development', href: '#services' },\n    { name: 'Business Automation', href: '#services' },\n    { name: 'AI Solutions', href: '#services' },\n    { name: 'Consulting', href: '#contact' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16 grid lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-6\">\n              <div className=\"relative\">\n                <Zap className=\"h-8 w-8 text-purple-400\" />\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse\"></div>\n              </div>\n              <span className=\"text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent\">\n                beam.tech\n              </span>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6\">\n              Transforming businesses through cutting-edge web design, automation, and AI solutions. \n              We beam your business to the future.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Mail className=\"h-5 w-5 text-purple-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Phone className=\"h-5 w-5 text-purple-400\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <MapPin className=\"h-5 w-5 text-purple-400\" />\n                <span>Margaret River and Canberra, Australia</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Services</h3>\n            <ul className=\"space-y-3\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <a\n                    href={service.href}\n                    className=\"text-gray-300 hover:text-purple-400 transition-colors duration-200\"\n                  >\n                    {service.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Stay Updated</h3>\n            <p className=\"text-gray-300 mb-4\">\n              Get the latest insights on web design, automation, and AI trends.\n            </p>\n            {newsletterStatus === 'success' ? (\n              <div className=\"text-center py-4\">\n                <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3\">\n                  <CheckCircle className=\"h-6 w-6 text-green-600 dark:text-green-400\" />\n                </div>\n                <p className=\"text-green-600 dark:text-green-400 text-sm\">\n                  {newsletterMessage}\n                </p>\n              </div>\n            ) : (\n              <form onSubmit={handleNewsletterSubmit} className=\"space-y-3\">\n                <div>\n                  <input\n                    type=\"email\"\n                    value={newsletterEmail}\n                    onChange={(e) => {\n                      setNewsletterEmail(e.target.value);\n                      if (emailError) setEmailError('');\n                    }}\n                    placeholder=\"Enter your email\"\n                    className={`w-full px-4 py-3 bg-gray-800 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-400 ${\n                      emailError ? 'border-red-500' : 'border-gray-700'\n                    }`}\n                    disabled={newsletterStatus === 'loading'}\n                  />\n                  {emailError && (\n                    <p className=\"mt-1 text-sm text-red-400 flex items-center\">\n                      <AlertCircle className=\"h-3 w-3 mr-1\" />\n                      {emailError}\n                    </p>\n                  )}\n                </div>\n\n                {newsletterMessage && newsletterStatus === 'error' && (\n                  <p className=\"text-sm text-red-400 flex items-center\">\n                    <AlertCircle className=\"h-3 w-3 mr-1\" />\n                    {newsletterMessage}\n                  </p>\n                )}\n\n                <button\n                  type=\"submit\"\n                  disabled={newsletterStatus === 'loading' || !newsletterEmail.trim()}\n                  className={`w-full font-semibold py-3 rounded-lg transition-all duration-200 ${\n                    newsletterStatus === 'loading' || !newsletterEmail.trim()\n                      ? 'bg-gray-600 cursor-not-allowed'\n                      : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-lg'\n                  } text-white`}\n                >\n                  {newsletterStatus === 'loading' ? (\n                    <span className=\"flex items-center justify-center\">\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                      Subscribing...\n                    </span>\n                  ) : (\n                    'Subscribe'\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        </div>\n", "suffix": "\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-800 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2025 Beam.tech. All rights reserved. Made with ❤️ in Margaret River and Canberra, Australia.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Privacy Policy\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white text-sm transition-colors duration-200\">\n                Terms of Service\n              </a>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 bg-gray-800 hover:bg-purple-600 rounded-lg text-gray-400 hover:text-white transition-all duration-200\"\n                aria-label=\"Scroll to top\"\n              >\n                <ArrowUp className=\"h-5 w-5\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;", "path": "client/src/components/Footer.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 22}}], ["87cd90f8-4f10-4fd2-a3c6-7aa9161cd9de", {"value": {"selectedCode": "", "prefix": "# Resend API Configuration\n", "suffix": "RESEND_API_KEY=re_GnJ6mX7b_NaZKsS4UXw4YPNi8RhaGSLDL\n# Notification email address\nNOTIFICATION_EMAIL=<EMAIL>\n\n# Database Configuration\nDATABASE_URL=your_database_url_here\n\n# Environment\nNODE_ENV=development\n", "path": ".env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["0bd4040d-4132-4401-b715-b0698b4fc480", {"value": {"selectedCode": "", "prefix": "import fs from 'fs';\n", "suffix": "import path from 'path';\n\n// Simple text logging utility for contact forms and newsletter subscriptions\n\n/**\n * Interface for contact form entries\n */\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\n/**\n * Interface for newsletter entries\n */\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n/**\n * Format contact form entry as text line\n */\nfunction formatContactFormEntry(entry: ContactFormEntry): string {\n  const cleanMessage = entry.message.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;\n}\n\n/**\n * Format newsletter entry as text line\n */\nfunction formatNewsletterEntry(entry: NewsletterEntry): string {\n  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;\n}\n\n/**\n * Get absolute log file path using direct path resolution\n */\nfunction getLogFilePath(filename: string): string {\n  // Use direct path resolution\n  const serverDir = path.resolve(__dirname);\n  const logDir = path.join(serverDir, 'logs');\n  return path.join(logDir, filename);\n}\n\n/**\n * Ensure log directory exists\n */\nasync function ensureLogDirectory(): Promise<string> {\n  const serverDir = path.resolve(__dirname);\n  const logDir = path.join(serverDir, 'logs');\n  \n  console.log(`📁 Ensuring log directory exists: ${logDir}`);\n  \n  try {\n    // Create directory if it doesn't exist\n    await fs.promises.mkdir(logDir, { recursive: true });\n    console.log(`✅ Log directory ready: ${logDir}`);\n    return logDir;\n  } catch (error) {\n    console.error(`❌ Error creating log directory:`, error);\n    throw error;\n  }\n}\n\n/**\n * Initialize log files\n */\nexport async function ensureLogFiles(): Promise<void> {\n  console.log('🔍 Initializing log files...');\n  \n  try {\n    // Ensure log directory exists\n    const logDir = await ensureLogDirectory();\n    \n    // Define file paths\n    const contactFile = path.join(logDir, 'contact-form-submissions.txt');\n    const newsletterFile = path.join(logDir, 'newsletter-subscriptions.txt');\n    \n    console.log(`📄 Contact log file: ${contactFile}`);\n    console.log(`📄 Newsletter log file: ${newsletterFile}`);\n    \n    // Touch files to create them if they don't exist\n    for (const file of [contactFile, newsletterFile]) {\n      try {\n        // Check if file exists, create if not\n        if (!fs.existsSync(file)) {\n          console.log(`� Creating log file: ${file}`);\n          await fs.promises.writeFile(file, '', 'utf8');\n          console.log(`✅ Created log file: ${file}`);\n        } else {\n          console.log(`✅ Log file already exists: ${file}`);\n        }\n      } catch (fileError) {\n        console.error(`❌ Error with log file ${file}:`, fileError);\n      }\n    }\n    \n    console.log('✅ Log files initialized successfully');\n  } catch (error) {\n    console.error('❌ Failed to initialize log files:', error);\n  }\n}\n\n/**\n * Append entry to text file\n */\nasync function appendToFile(filePath: string, content: string): Promise<void> {\n  console.log(`� Appending to file: ${filePath}`);\n  \n  try {\n    // Ensure directory exists\n    await ensureLogDirectory();\n    \n    // Append to file with newline\n    await fs.promises.appendFile(filePath, content + '\\n', 'utf8');\n    console.log(`✅ Successfully appended to file: ${filePath}`);\n  } catch (error) {\n    console.error(`❌ Error appending to file:`, error);\n    throw error;\n  }\n}\n\n/**\n * Log contact form submission\n */\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  console.log(`� Logging contact form submission for: ${entry.email}`);\n  \n  try {\n    // Get log file path\n    const filePath = getLogFilePath('contact-form-submissions.txt');\n    console.log(`� Using log file: ${filePath}`);\n    \n    // Format entry as text\n    const textLine = formatContactFormEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n    \n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Contact form submission logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log contact form submission:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Log newsletter subscription\n */\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  console.log(`� Logging newsletter subscription for: ${entry.email}`);\n  \n  try {\n    // Get log file path\n    const filePath = getLogFilePath('newsletter-subscriptions.txt');\n    console.log(`� Using log file: ${filePath}`);\n    \n    // Format entry as text\n    const textLine = formatNewsletterEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n    \n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Newsletter subscription logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log newsletter subscription:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Get current ISO timestamp\n */\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n/**\n * Create contact form entry\n */\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  console.log(`🔍 Creating contact form entry for: ${email} (${status})`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n/**\n * Create newsletter entry\n */\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  console.log(`🔍 Creating newsletter entry for: ${email} (${status})`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n", "path": "server/csvLogger.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}]]