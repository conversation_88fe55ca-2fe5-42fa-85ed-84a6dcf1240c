{"/home/<USER>/workspace/": {"rootPath": "/home/<USER>/workspace", "relPath": ""}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/"}, "/home/<USER>/workspace/shared/": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/"}, "/home/<USER>/workspace/server/": {"rootPath": "/home/<USER>/workspace", "relPath": "server/"}, "/home/<USER>/workspace/client/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/"}, "/home/<USER>/workspace/client/src/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/"}, "/home/<USER>/workspace/client/src/types/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/"}, "/home/<USER>/workspace/client/src/hooks/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/"}, "/home/<USER>/workspace/client/src/components/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/"}, "/home/<USER>/workspace/.upm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/"}, "/home/<USER>/workspace/.local/state/replit/agent/": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/"}, "/home/<USER>/workspace/.config/.vscode-server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/resources/icons/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/resources/icons/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/resources/icons/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/resources/icons/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/l10n/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.0/assets/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.333.0/assets/status/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.333.0/assets/status/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/next-edit/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/light/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/media/keyboard/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.481.0/common-webviews/assets/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/"}, "/home/<USER>/workspace/.config/.vscode-server/data/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/output_logging_20250613T011838/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/output_logging_20250613T011838/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/output_logging_20250613T011811/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/output_logging_20250613T011811/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/assignableUsers/eddie333016/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/assignableUsers/eddie333016/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedProfilesData/__default__profile__/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedProfilesData/__default__profile__/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/contrib/terminal/common/scripts/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/resources/walkthroughs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/.vscode/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/tunnel-forwarding/.vscode/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/terminal-suggest/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/simple-browser/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/search-result/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/references-view/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/php-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/images/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/npm/images/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.vscode-js-profile-table/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/base/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/typescript-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug-companion/out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/targets/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/targets/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/light/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/resources/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/merge-conflict/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/media-preview/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/preview-styles/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/preview-styles/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/lib/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/server/lib/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/html-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/gulp/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/grunt/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github-authentication/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/terminal/node/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/files/node/watcher/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/platform/files/node/watcher/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/vendor/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ms-vscode.js-debug/src/ui/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-math/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/markdown-language-features/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/json-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/jake/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git-base/languages/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/light/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/resources/icons/dark/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/extension-editing/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/emmet/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-server-ready/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/.vscode/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/debug-auto-launch/.vscode/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/server/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/configuration-editing/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/"}, "/home/<USER>/workspace/.cache/replit/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/"}, "/home/<USER>/workspace/.cache/replit/nix/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/"}, "/home/<USER>/workspace/.cache/replit/modules/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/"}, "/home/<USER>/workspace/.cache/replit/env/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/microsoft-authentication/media/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/notebook-out/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/ipynb/notebook-out/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/schemas/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/css-language-features/schemas/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.css-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost2/vscode.css-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2752f7d5/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2752f7d5/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-53ad80eb/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-53ad80eb/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5e6c2324/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5e6c2324/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e2aae41/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e2aae41/"}, "/home/<USER>/workspace/client/src/utils/": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-327b1cf9/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-327b1cf9/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-45e5f64c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-45e5f64c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5ddef898/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5ddef898/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/7d44b9c4/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/7d44b9c4/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/428efd6e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/428efd6e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-18598ba4/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-18598ba4/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/689aff0c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/689aff0c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7a75ac05/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7a75ac05/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5980142/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5980142/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/24165b42/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/24165b42/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/output_logging_20250613T040037/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/output_logging_20250613T040037/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost5/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/output_logging_20250613T021533/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/output_logging_20250613T021533/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost4/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/output_logging_20250613T021211/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/output_logging_20250613T021211/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T011807/exthost3/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T011807/exthost3/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/task-storage/tasks/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/task-storage/tasks/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/task-storage/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/task-storage/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/agent-edits/shards/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-global-state/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-1/Augment.vscode-augment/augment-global-state/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2994dc39/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2994dc39/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7b682c69/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7b682c69/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-488ff9da/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-488ff9da/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-391f9dfc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-391f9dfc/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-3079d69a/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-3079d69a/"}, "/home/<USER>/workspace/.cache/typescript/5.8/": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce-2/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/output_logging_20250613T041203/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/output_logging_20250613T041203/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1b32cde/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1b32cde/"}, "/home/<USER>/workspace/server/logs/": {"rootPath": "/home/<USER>/workspace", "relPath": "server/logs/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-cba825e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-cba825e/"}, "/home/<USER>/workspace/.vscode/": {"rootPath": "/home/<USER>/workspace", "relPath": ".vscode/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.markdown-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost1/vscode.markdown-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5447ff93/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5447ff93/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4f6eb2d1/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4f6eb2d1/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-5d328adc/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-5d328adc/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-35b4ea7e/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-35b4ea7e/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-351f7ed2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-351f7ed2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2e4ca6be/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2e4ca6be/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.335.0/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.335.0/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.335.0/syntaxes/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.335.0/syntaxes/"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.335.0/assets/status/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.335.0/assets/status/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/output_logging_20250613T090348/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/output_logging_20250613T090348/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost2/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost2/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/GitHub.copilot-chat/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/GitHub.copilot-chat/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.typescript-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.typescript-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.git/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.git/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/GitHub.vscode-pull-request-github/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/GitHub.vscode-pull-request-github/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/GitHub.copilot/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/GitHub.copilot/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/Augment.vscode-augment/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/Augment.vscode-augment/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/output_logging_20250613T090726/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/output_logging_20250613T090726/"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.json-language-features/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.json-language-features/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/88fe55ca-2fe5-42fa-85ed-84a6dcf1240c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/88fe55ca-2fe5-42fa-85ed-84a6dcf1240c/"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/31e8c00c/": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/31e8c00c/"}}