{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/Services.tsx"}, "originalCode": "import React, { useState } from 'react';\nimport { Code, Bot, Zap, ArrowRight, CheckCircle, Settings } from 'lucide-react';\nimport { scrollToContact } from '../utils/navigation';\nimport QuickQuoteModal from './QuickQuoteModal';\n\nconst Services: React.FC = () => {\n  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);\n\n  const services = [\n    {\n      id: 'web-design',\n      title: 'Web Design & Development',\n      description: 'Custom websites and web applications built with cutting-edge technologies',\n      icon: Code,\n      features: [\n        'Responsive Design',\n        'Performance Optimization',\n        'SEO Integration',\n        'Modern Frameworks',\n        'Cross-browser Compatibility'\n      ],\n      color: 'from-purple-500 to-blue-500'\n    },\n    {\n      id: 'automation',\n      title: 'Business Automation',\n      description: 'Streamline your workflows with intelligent automation solutions',\n      icon: Zap,\n      features: [\n        'Process Automation',\n        'Data Integration',\n        'Workflow Optimization',\n        'Custom APIs',\n        'Third-party Integrations'\n      ],\n      color: 'from-blue-500 to-cyan-500'\n    },\n    {\n      id: 'ai',\n      title: 'AI Solutions',\n      description: 'Harness the power of artificial intelligence for your business',\n      icon: Bot,\n      features: [\n        'Machine Learning Models',\n        'Natural Language Processing',\n        'Computer Vision',\n        'Predictive Analytics',\n        'AI-powered Chatbots'\n      ],\n      color: 'from-purple-500 to-pink-500'\n    },\n    {\n      id: 'small-business',\n      title: 'Small Business Essentials',\n      description: 'Empowering small businesses with reliable cloud hosting, enhanced websites, and essential tools',\n      icon: Settings,\n      features: [\n        'Affordable Cloud Hosting',\n        'Contact & Booking Forms',\n        'Basic Web Enhancements',\n        'Fast Performance Tweaks',\n        'Email & Domain Setup'\n      ],\n      color: 'from-green-500 to-teal-500',\n      hasQuickQuote: true\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Services</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            We deliver comprehensive digital solutions that drive growth and innovation\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {services.map((service) => {\n            const IconComponent = service.icon;\n            return (\n              <div\n                key={service.id}\n                className=\"group relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700\"\n              >\n                {/* Background Gradient */}\n                <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>\n                \n                {/* Icon */}\n                <div className={`inline-flex p-4 bg-gradient-to-r ${service.color} rounded-xl mb-6`}>\n                  <IconComponent className=\"h-8 w-8 text-white\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                  {service.title}\n                </h3>\n                \n                <p className=\"text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n\n                {/* Features */}\n                <ul className=\"space-y-3 mb-8\">\n                  {service.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center space-x-3\">\n                      <CheckCircle className=\"h-5 w-5 text-green-500 flex-shrink-0\" />\n                      <span className=\"text-gray-700 dark:text-gray-300\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n\n                {/* CTA Button */}\n                <button\n                  onClick={scrollToContact}\n                  className=\"group/btn inline-flex items-center justify-center w-full px-6 py-3 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-semibold rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200\"\n                >\n                  <span>Learn More</span>\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover/btn:translate-x-1 transition-transform duration-200\" />\n                </button>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-8\">\n            Ready to transform your business? Let's discuss your project.\n          </p>\n          <button\n            onClick={scrollToContact}\n            className=\"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\"\n          >\n            Get Started Today\n            <ArrowRight className=\"ml-2 h-5 w-5\" />\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;", "modifiedCode": "import React, { useState } from 'react';\nimport { Code, Bot, Zap, ArrowRight, CheckCircle, Settings } from 'lucide-react';\nimport { scrollToContact } from '../utils/navigation';\nimport QuickQuoteModal from './QuickQuoteModal';\n\nconst Services: React.FC = () => {\n  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);\n\n  const services = [\n    {\n      id: 'web-design',\n      title: 'Web Design & Development',\n      description: 'Custom websites and web applications built with cutting-edge technologies',\n      icon: Code,\n      features: [\n        'Responsive Design',\n        'Performance Optimization',\n        'SEO Integration',\n        'Modern Frameworks',\n        'Cross-browser Compatibility'\n      ],\n      color: 'from-purple-500 to-blue-500'\n    },\n    {\n      id: 'automation',\n      title: 'Business Automation',\n      description: 'Streamline your workflows with intelligent automation solutions',\n      icon: Zap,\n      features: [\n        'Process Automation',\n        'Data Integration',\n        'Workflow Optimization',\n        'Custom APIs',\n        'Third-party Integrations'\n      ],\n      color: 'from-blue-500 to-cyan-500'\n    },\n    {\n      id: 'ai',\n      title: 'AI Solutions',\n      description: 'Harness the power of artificial intelligence for your business',\n      icon: Bot,\n      features: [\n        'Machine Learning Models',\n        'Natural Language Processing',\n        'Computer Vision',\n        'Predictive Analytics',\n        'AI-powered Chatbots'\n      ],\n      color: 'from-purple-500 to-pink-500'\n    },\n    {\n      id: 'small-business',\n      title: 'Small Business Essentials',\n      description: 'Empowering small businesses with reliable cloud hosting, enhanced websites, and essential tools',\n      icon: Settings,\n      features: [\n        'Affordable Cloud Hosting',\n        'Contact & Booking Forms',\n        'Basic Web Enhancements',\n        'Fast Performance Tweaks',\n        'Email & Domain Setup'\n      ],\n      color: 'from-green-500 to-teal-500',\n      hasQuickQuote: true\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"py-20 bg-white dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center max-w-3xl mx-auto mb-16\">\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6\">\n            Our <span className=\"bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent\">Services</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n            We deliver comprehensive digital solutions that drive growth and innovation\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {services.map((service) => {\n            const IconComponent = service.icon;\n            return (\n              <div\n                key={service.id}\n                className=\"group relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100 dark:border-gray-700\"\n              >\n                {/* Background Gradient */}\n                <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>\n                \n                {/* Icon */}\n                <div className={`inline-flex p-4 bg-gradient-to-r ${service.color} rounded-xl mb-6`}>\n                  <IconComponent className=\"h-8 w-8 text-white\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n                  {service.title}\n                </h3>\n                \n                <p className=\"text-gray-600 dark:text-gray-300 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n\n                {/* Features */}\n                <ul className=\"space-y-3 mb-8\">\n                  {service.features.map((feature, index) => (\n                    <li key={index} className=\"flex items-center space-x-3\">\n                      <CheckCircle className=\"h-5 w-5 text-green-500 flex-shrink-0\" />\n                      <span className=\"text-gray-700 dark:text-gray-300\">{feature}</span>\n                    </li>\n                  ))}\n                </ul>\n\n                {/* CTA Button */}\n                <button\n                  onClick={scrollToContact}\n                  className=\"group/btn inline-flex items-center justify-center w-full px-6 py-3 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-semibold rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-all duration-200\"\n                >\n                  <span>Learn More</span>\n                  <ArrowRight className=\"ml-2 h-5 w-5 group-hover/btn:translate-x-1 transition-transform duration-200\" />\n                </button>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-16\">\n          <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-8\">\n            Ready to transform your business? Let's discuss your project.\n          </p>\n          <button\n            onClick={scrollToContact}\n            className=\"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300\"\n          >\n            Get Started Today\n            <ArrowRight className=\"ml-2 h-5 w-5\" />\n          </button>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;"}