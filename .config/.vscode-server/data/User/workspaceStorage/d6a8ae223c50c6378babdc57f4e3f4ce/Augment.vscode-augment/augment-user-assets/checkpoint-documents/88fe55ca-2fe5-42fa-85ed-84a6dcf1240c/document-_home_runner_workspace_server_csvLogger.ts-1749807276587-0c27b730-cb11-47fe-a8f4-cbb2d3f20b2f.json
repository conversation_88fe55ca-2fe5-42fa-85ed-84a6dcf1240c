{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/csvLogger.ts"}, "originalCode": "import fs from 'fs';\nimport path from 'path';\n\n// Simple text logging utility for contact forms and newsletter subscriptions\n\n/**\n * Interface for contact form entries\n */\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\n/**\n * Interface for newsletter entries\n */\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n/**\n * Interface for quick quote entries\n */\nexport interface QuickQuoteEntry {\n  timestamp: string;\n  service: string;\n  service_other: string;\n  website_url: string;\n  description: string;\n  email: string;\n  phone: string;\n  submission_status: 'success' | 'failed';\n}\n\n/**\n * Format contact form entry as text line\n */\nfunction formatContactFormEntry(entry: ContactFormEntry): string {\n  const cleanMessage = entry.message.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;\n}\n\n/**\n * Format newsletter entry as text line\n */\nfunction formatNewsletterEntry(entry: NewsletterEntry): string {\n  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;\n}\n\n/**\n * Format quick quote entry as text line\n */\nfunction formatQuickQuoteEntry(entry: QuickQuoteEntry): string {\n  const cleanDescription = entry.description.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  const serviceText = entry.service === 'other' ? `${entry.service} (${entry.service_other})` : entry.service;\n  return `[${entry.timestamp}] QUOTE: ${entry.email} | Service: ${serviceText} | Website: ${entry.website_url || 'N/A'} | Phone: ${entry.phone || 'N/A'} | Description: ${cleanDescription} | Status: ${entry.submission_status}`;\n}\n\n/**\n * Get absolute log file path using direct path resolution\n */\nfunction getLogFilePath(filename: string): string {\n  // Use ES module compatible path resolution\n  const serverDir = path.dirname(new URL(import.meta.url).pathname);\n  const logDir = path.join(serverDir, 'logs');\n  return path.join(logDir, filename);\n}\n\n/**\n * Ensure log directory exists\n */\nasync function ensureLogDirectory(): Promise<string> {\n  const serverDir = path.dirname(new URL(import.meta.url).pathname);\n  const logDir = path.join(serverDir, 'logs');\n  \n  console.log(`📁 Ensuring log directory exists: ${logDir}`);\n  \n  try {\n    // Create directory if it doesn't exist\n    await fs.promises.mkdir(logDir, { recursive: true });\n    console.log(`✅ Log directory ready: ${logDir}`);\n    return logDir;\n  } catch (error) {\n    console.error(`❌ Error creating log directory:`, error);\n    throw error;\n  }\n}\n\n/**\n * Initialize log files\n */\nexport async function ensureLogFiles(): Promise<void> {\n  console.log('🔍 Initializing log files...');\n  \n  try {\n    // Ensure log directory exists\n    const logDir = await ensureLogDirectory();\n    \n    // Define file paths\n    const contactFile = path.join(logDir, 'contact-form-submissions.txt');\n    const newsletterFile = path.join(logDir, 'newsletter-subscriptions.txt');\n    const quickQuoteFile = path.join(logDir, 'quick-quote-submissions.txt');\n\n    console.log(`📄 Contact log file: ${contactFile}`);\n    console.log(`📄 Newsletter log file: ${newsletterFile}`);\n    console.log(`📄 Quick Quote log file: ${quickQuoteFile}`);\n    \n    // Touch files to create them if they don't exist\n    for (const file of [contactFile, newsletterFile]) {\n      try {\n        // Check if file exists, create if not\n        if (!fs.existsSync(file)) {\n          console.log(`� Creating log file: ${file}`);\n          await fs.promises.writeFile(file, '', 'utf8');\n          console.log(`✅ Created log file: ${file}`);\n        } else {\n          console.log(`✅ Log file already exists: ${file}`);\n        }\n      } catch (fileError) {\n        console.error(`❌ Error with log file ${file}:`, fileError);\n      }\n    }\n    \n    console.log('✅ Log files initialized successfully');\n  } catch (error) {\n    console.error('❌ Failed to initialize log files:', error);\n  }\n}\n\n/**\n * Append entry to text file\n */\nasync function appendToFile(filePath: string, content: string): Promise<void> {\n  console.log(`� Appending to file: ${filePath}`);\n  \n  try {\n    // Ensure directory exists\n    await ensureLogDirectory();\n    \n    // Append to file with newline\n    await fs.promises.appendFile(filePath, content + '\\n', 'utf8');\n    console.log(`✅ Successfully appended to file: ${filePath}`);\n  } catch (error) {\n    console.error(`❌ Error appending to file:`, error);\n    throw error;\n  }\n}\n\n/**\n * Log contact form submission\n */\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  console.log(`� Logging contact form submission for: ${entry.email}`);\n  \n  try {\n    // Get log file path\n    const filePath = getLogFilePath('contact-form-submissions.txt');\n    console.log(`� Using log file: ${filePath}`);\n    \n    // Format entry as text\n    const textLine = formatContactFormEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n    \n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Contact form submission logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log contact form submission:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Log newsletter subscription\n */\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  console.log(`� Logging newsletter subscription for: ${entry.email}`);\n\n  try {\n    // Get log file path\n    const filePath = getLogFilePath('newsletter-subscriptions.txt');\n    console.log(`� Using log file: ${filePath}`);\n\n    // Format entry as text\n    const textLine = formatNewsletterEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n\n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Newsletter subscription logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log newsletter subscription:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Log quick quote submission\n */\nexport async function logQuickQuoteSubmission(entry: QuickQuoteEntry): Promise<void> {\n  console.log(`📝 Logging quick quote submission for: ${entry.email}`);\n\n  try {\n    // Get log file path\n    const filePath = getLogFilePath('quick-quote-submissions.txt');\n    console.log(`📁 Using log file: ${filePath}`);\n\n    // Format entry as text\n    const textLine = formatQuickQuoteEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n\n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Quick quote submission logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log quick quote submission:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Get current ISO timestamp\n */\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n/**\n * Create contact form entry\n */\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  console.log(`🔍 Creating contact form entry for: ${email} (${status})`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n/**\n * Create newsletter entry\n */\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  console.log(`🔍 Creating newsletter entry for: ${email} (${status})`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n", "modifiedCode": "import fs from 'fs';\nimport path from 'path';\n\n// Simple text logging utility for contact forms and newsletter subscriptions\n\n/**\n * Interface for contact form entries\n */\nexport interface ContactFormEntry {\n  timestamp: string;\n  name: string;\n  email: string;\n  company: string;\n  project_type: string;\n  budget: string;\n  message: string;\n  submission_status: 'success' | 'failed';\n}\n\n/**\n * Interface for newsletter entries\n */\nexport interface NewsletterEntry {\n  timestamp: string;\n  email: string;\n  subscription_status: 'success' | 'failed';\n}\n\n/**\n * Interface for quick quote entries\n */\nexport interface QuickQuoteEntry {\n  timestamp: string;\n  service: string;\n  service_other: string;\n  website_url: string;\n  description: string;\n  email: string;\n  phone: string;\n  submission_status: 'success' | 'failed';\n}\n\n/**\n * Format contact form entry as text line\n */\nfunction formatContactFormEntry(entry: ContactFormEntry): string {\n  const cleanMessage = entry.message.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  return `[${entry.timestamp}] CONTACT: ${entry.name} <${entry.email}> | Company: ${entry.company || 'N/A'} | Project: ${entry.project_type} | Budget: ${entry.budget} | Message: ${cleanMessage} | Status: ${entry.submission_status}`;\n}\n\n/**\n * Format newsletter entry as text line\n */\nfunction formatNewsletterEntry(entry: NewsletterEntry): string {\n  return `[${entry.timestamp}] NEWSLETTER: ${entry.email} | Status: ${entry.subscription_status}`;\n}\n\n/**\n * Format quick quote entry as text line\n */\nfunction formatQuickQuoteEntry(entry: QuickQuoteEntry): string {\n  const cleanDescription = entry.description.replace(/\\n/g, ' ').replace(/\\r/g, ' ').substring(0, 200);\n  const serviceText = entry.service === 'other' ? `${entry.service} (${entry.service_other})` : entry.service;\n  return `[${entry.timestamp}] QUOTE: ${entry.email} | Service: ${serviceText} | Website: ${entry.website_url || 'N/A'} | Phone: ${entry.phone || 'N/A'} | Description: ${cleanDescription} | Status: ${entry.submission_status}`;\n}\n\n/**\n * Get absolute log file path using direct path resolution\n */\nfunction getLogFilePath(filename: string): string {\n  // Use ES module compatible path resolution\n  const serverDir = path.dirname(new URL(import.meta.url).pathname);\n  const logDir = path.join(serverDir, 'logs');\n  return path.join(logDir, filename);\n}\n\n/**\n * Ensure log directory exists\n */\nasync function ensureLogDirectory(): Promise<string> {\n  const serverDir = path.dirname(new URL(import.meta.url).pathname);\n  const logDir = path.join(serverDir, 'logs');\n  \n  console.log(`📁 Ensuring log directory exists: ${logDir}`);\n  \n  try {\n    // Create directory if it doesn't exist\n    await fs.promises.mkdir(logDir, { recursive: true });\n    console.log(`✅ Log directory ready: ${logDir}`);\n    return logDir;\n  } catch (error) {\n    console.error(`❌ Error creating log directory:`, error);\n    throw error;\n  }\n}\n\n/**\n * Initialize log files\n */\nexport async function ensureLogFiles(): Promise<void> {\n  console.log('🔍 Initializing log files...');\n  \n  try {\n    // Ensure log directory exists\n    const logDir = await ensureLogDirectory();\n    \n    // Define file paths\n    const contactFile = path.join(logDir, 'contact-form-submissions.txt');\n    const newsletterFile = path.join(logDir, 'newsletter-subscriptions.txt');\n    const quickQuoteFile = path.join(logDir, 'quick-quote-submissions.txt');\n\n    console.log(`📄 Contact log file: ${contactFile}`);\n    console.log(`📄 Newsletter log file: ${newsletterFile}`);\n    console.log(`📄 Quick Quote log file: ${quickQuoteFile}`);\n    \n    // Touch files to create them if they don't exist\n    for (const file of [contactFile, newsletterFile]) {\n      try {\n        // Check if file exists, create if not\n        if (!fs.existsSync(file)) {\n          console.log(`� Creating log file: ${file}`);\n          await fs.promises.writeFile(file, '', 'utf8');\n          console.log(`✅ Created log file: ${file}`);\n        } else {\n          console.log(`✅ Log file already exists: ${file}`);\n        }\n      } catch (fileError) {\n        console.error(`❌ Error with log file ${file}:`, fileError);\n      }\n    }\n    \n    console.log('✅ Log files initialized successfully');\n  } catch (error) {\n    console.error('❌ Failed to initialize log files:', error);\n  }\n}\n\n/**\n * Append entry to text file\n */\nasync function appendToFile(filePath: string, content: string): Promise<void> {\n  console.log(`� Appending to file: ${filePath}`);\n  \n  try {\n    // Ensure directory exists\n    await ensureLogDirectory();\n    \n    // Append to file with newline\n    await fs.promises.appendFile(filePath, content + '\\n', 'utf8');\n    console.log(`✅ Successfully appended to file: ${filePath}`);\n  } catch (error) {\n    console.error(`❌ Error appending to file:`, error);\n    throw error;\n  }\n}\n\n/**\n * Log contact form submission\n */\nexport async function logContactFormSubmission(entry: ContactFormEntry): Promise<void> {\n  console.log(`� Logging contact form submission for: ${entry.email}`);\n  \n  try {\n    // Get log file path\n    const filePath = getLogFilePath('contact-form-submissions.txt');\n    console.log(`� Using log file: ${filePath}`);\n    \n    // Format entry as text\n    const textLine = formatContactFormEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n    \n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Contact form submission logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log contact form submission:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Log newsletter subscription\n */\nexport async function logNewsletterSubscription(entry: NewsletterEntry): Promise<void> {\n  console.log(`� Logging newsletter subscription for: ${entry.email}`);\n\n  try {\n    // Get log file path\n    const filePath = getLogFilePath('newsletter-subscriptions.txt');\n    console.log(`� Using log file: ${filePath}`);\n\n    // Format entry as text\n    const textLine = formatNewsletterEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n\n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Newsletter subscription logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log newsletter subscription:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Log quick quote submission\n */\nexport async function logQuickQuoteSubmission(entry: QuickQuoteEntry): Promise<void> {\n  console.log(`📝 Logging quick quote submission for: ${entry.email}`);\n\n  try {\n    // Get log file path\n    const filePath = getLogFilePath('quick-quote-submissions.txt');\n    console.log(`📁 Using log file: ${filePath}`);\n\n    // Format entry as text\n    const textLine = formatQuickQuoteEntry(entry);\n    console.log(`🔍 Formatted log entry: ${textLine}`);\n\n    // Write to file\n    await appendToFile(filePath, textLine);\n    console.log(`✅ Quick quote submission logged successfully: ${entry.email}`);\n  } catch (error) {\n    console.error(`❌ Failed to log quick quote submission:`, error);\n    // Do not throw to prevent breaking application flow\n  }\n}\n\n/**\n * Get current ISO timestamp\n */\nexport function getCurrentTimestamp(): string {\n  return new Date().toISOString();\n}\n\n/**\n * Create contact form entry\n */\nexport function createContactFormEntry(\n  name: string,\n  email: string,\n  company: string,\n  project: string,\n  budget: string,\n  message: string,\n  status: 'success' | 'failed'\n): ContactFormEntry {\n  console.log(`🔍 Creating contact form entry for: ${email} (${status})`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    name: name || '',\n    email: email || '',\n    company: company || '',\n    project_type: project || '',\n    budget: budget || '',\n    message: message || '',\n    submission_status: status\n  };\n}\n\n/**\n * Create newsletter entry\n */\nexport function createNewsletterEntry(\n  email: string,\n  status: 'success' | 'failed'\n): NewsletterEntry {\n  console.log(`🔍 Creating newsletter entry for: ${email} (${status})`);\n  return {\n    timestamp: getCurrentTimestamp(),\n    email: email || '',\n    subscription_status: status\n  };\n}\n"}