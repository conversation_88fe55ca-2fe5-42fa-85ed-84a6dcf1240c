{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/QuickQuoteModal.tsx"}, "modifiedCode": "import React, { useState, useEffect } from 'react';\nimport { X, <PERSON><PERSON><PERSON>, ArrowLeft, CheckCircle, Loader2, AlertCircle } from 'lucide-react';\nimport { useForm } from '../hooks/useForm';\nimport { validateQuickQuoteForm, type QuickQuoteFormData } from '../utils/validation';\nimport { submitQuickQuoteForm } from '../utils/emailService';\n\ninterface QuickQuoteModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst QuickQuoteModal: React.FC<QuickQuoteModalProps> = ({ isOpen, onClose }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const totalSteps = 4;\n\n  const {\n    values,\n    errors,\n    isSubmitting,\n    isSubmitted,\n    submitMessage,\n    handleChange,\n    handleSubmit,\n    setFieldValue,\n    resetForm\n  } = useForm<QuickQuoteFormData>({\n    initialValues: {\n      service: '',\n      serviceOther: '',\n      websiteUrl: '',\n      description: '',\n      email: '',\n      phone: ''\n    },\n    validate: (values) => validateQuickQuoteForm(values).errors,\n    onSubmit: async (values) => {\n      const result = await submitQuickQuoteForm(values);\n      return result;\n    }\n  });\n\n  // Handle modal close\n  const handleClose = () => {\n    if (!isSubmitting) {\n      onClose();\n      setTimeout(() => {\n        setCurrentStep(1);\n        resetForm();\n      }, 300);\n    }\n  };\n\n  // Handle escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && isOpen && !isSubmitting) {\n        handleClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, isSubmitting]);\n\n  // Handle backdrop click\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget && !isSubmitting) {\n      handleClose();\n    }\n  };\n\n  // Navigation functions\n  const nextStep = () => {\n    if (currentStep < totalSteps) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  // Handle service selection\n  const handleServiceSelect = (service: string) => {\n    setFieldValue('service', service);\n    if (service !== 'other') {\n      setFieldValue('serviceOther', '');\n    }\n  };\n\n  // Handle form submission\n  const onFormSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    await handleSubmit(e);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div \n      className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n      onClick={handleBackdropClick}\n    >\n      <div \n        className=\"relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100 max-h-[90vh] overflow-hidden\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Quick Quote\n            </h2>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n              Step {currentStep} of {totalSteps}\n            </p>\n          </div>\n          <button\n            onClick={handleClose}\n            disabled={isSubmitting}\n            className=\"p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-900\">\n          <div className=\"flex items-center space-x-2\">\n            {Array.from({ length: totalSteps }, (_, i) => (\n              <div\n                key={i}\n                className={`flex-1 h-2 rounded-full transition-colors duration-300 ${\n                  i + 1 <= currentStep\n                    ? 'bg-gradient-to-r from-green-500 to-teal-500'\n                    : 'bg-gray-200 dark:bg-gray-700'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[60vh]\">\n          {isSubmitted ? (\n            // Success State\n            <div className=\"text-center py-8\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full mb-6\">\n                <CheckCircle className=\"h-8 w-8 text-green-500\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Quote Request Submitted!\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                Thanks! We'll get back to you with a quote within 1 business day.\n              </p>\n              <button\n                onClick={handleClose}\n                className=\"inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-green-500 to-teal-500 text-white font-semibold rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200\"\n              >\n                Close\n              </button>\n            </div>\n          ) : (\n            <form onSubmit={onFormSubmit} className=\"space-y-6\">\n              {/* Step 1: Service Selection */}\n              {currentStep === 1 && (\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                      What service do you need?\n                    </h3>\n                    <div className=\"space-y-3\">\n                      {[\n                        { value: 'hosting', label: 'Cloud hosting setup' },\n                        { value: 'forms', label: 'Add a form (contact, booking, etc.)' },\n                        { value: 'speed', label: 'Speed up my website' },\n                        { value: 'fix', label: 'Fix something that\\'s broken' },\n                        { value: 'other', label: 'Something else' }\n                      ].map((option) => (\n                        <label\n                          key={option.value}\n                          className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${\n                            values.service === option.value\n                              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'\n                          }`}\n                        >\n                          <input\n                            type=\"radio\"\n                            name=\"service\"\n                            value={option.value}\n                            checked={values.service === option.value}\n                            onChange={() => handleServiceSelect(option.value)}\n                            className=\"sr-only\"\n                          />\n                          <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                            values.service === option.value\n                              ? 'border-green-500 bg-green-500'\n                              : 'border-gray-300 dark:border-gray-600'\n                          }`}>\n                            {values.service === option.value && (\n                              <div className=\"w-2 h-2 bg-white rounded-full\" />\n                            )}\n                          </div>\n                          <span className=\"text-gray-900 dark:text-white font-medium\">\n                            {option.label}\n                          </span>\n                        </label>\n                      ))}\n                    </div>\n                    \n                    {values.service === 'other' && (\n                      <div className=\"mt-4\">\n                        <input\n                          type=\"text\"\n                          name=\"serviceOther\"\n                          value={values.serviceOther}\n                          onChange={handleChange}\n                          placeholder=\"Please describe what you need...\"\n                          className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-200\"\n                        />\n                        {errors.serviceOther && (\n                          <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                            <AlertCircle className=\"h-4 w-4 mr-1\" />\n                            {errors.serviceOther}\n                          </p>\n                        )}\n                      </div>\n                    )}\n                    \n                    {errors.service && (\n                      <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.service}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Step 2: Website URL */}\n              {currentStep === 2 && (\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                      What's your website URL?\n                    </h3>\n                    <input\n                      type=\"url\"\n                      name=\"websiteUrl\"\n                      value={values.websiteUrl}\n                      onChange={handleChange}\n                      placeholder=\"https://yourwebsite.com\"\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-200\"\n                    />\n                    {errors.websiteUrl && (\n                      <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.websiteUrl}\n                      </p>\n                    )}\n                    <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n                      If you don't have a website yet, just type \"none\" or leave it blank.\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              {/* Step 3: Description */}\n              {currentStep === 3 && (\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                      Tell us more about what you need\n                    </h3>\n                    <textarea\n                      name=\"description\"\n                      value={values.description}\n                      onChange={handleChange}\n                      rows={6}\n                      placeholder=\"I want to add a booking form to my existing site...\"\n                      className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-200 resize-none\"\n                    />\n                    {errors.description && (\n                      <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                        <AlertCircle className=\"h-4 w-4 mr-1\" />\n                        {errors.description}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              )}\n\n              {/* Step 4: Contact Info */}\n              {currentStep === 4 && (\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                      How can we reach you?\n                    </h3>\n                    <div className=\"space-y-4\">\n                      <div>\n                        <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Email Address *\n                        </label>\n                        <input\n                          type=\"email\"\n                          id=\"email\"\n                          name=\"email\"\n                          value={values.email}\n                          onChange={handleChange}\n                          placeholder=\"<EMAIL>\"\n                          className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-200\"\n                        />\n                        {errors.email && (\n                          <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                            <AlertCircle className=\"h-4 w-4 mr-1\" />\n                            {errors.email}\n                          </p>\n                        )}\n                      </div>\n                      \n                      <div>\n                        <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          Phone Number (Optional)\n                        </label>\n                        <input\n                          type=\"tel\"\n                          id=\"phone\"\n                          name=\"phone\"\n                          value={values.phone}\n                          onChange={handleChange}\n                          placeholder=\"+61 400 000 000\"\n                          className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors duration-200\"\n                        />\n                        {errors.phone && (\n                          <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\n                            <AlertCircle className=\"h-4 w-4 mr-1\" />\n                            {errors.phone}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </form>\n          )}\n        </div>\n\n        {/* Footer */}\n        {!isSubmitted && (\n          <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\">\n            <button\n              type=\"button\"\n              onClick={prevStep}\n              disabled={currentStep === 1 || isSubmitting}\n              className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back\n            </button>\n\n            {currentStep < totalSteps ? (\n              <button\n                type=\"button\"\n                onClick={nextStep}\n                disabled={\n                  isSubmitting ||\n                  (currentStep === 1 && !values.service) ||\n                  (currentStep === 1 && values.service === 'other' && !values.serviceOther)\n                }\n                className=\"inline-flex items-center px-6 py-2 bg-gradient-to-r from-green-500 to-teal-500 text-white font-medium rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n              >\n                Next\n                <ArrowRight className=\"h-4 w-4 ml-2\" />\n              </button>\n            ) : (\n              <button\n                type=\"submit\"\n                onClick={onFormSubmit}\n                disabled={isSubmitting || !values.email}\n                className=\"inline-flex items-center px-6 py-2 bg-gradient-to-r from-green-500 to-teal-500 text-white font-medium rounded-lg hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    Submit Quote Request\n                    <ArrowRight className=\"h-4 w-4 ml-2\" />\n                  </>\n                )}\n              </button>\n            )}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default QuickQuoteModal;\n"}