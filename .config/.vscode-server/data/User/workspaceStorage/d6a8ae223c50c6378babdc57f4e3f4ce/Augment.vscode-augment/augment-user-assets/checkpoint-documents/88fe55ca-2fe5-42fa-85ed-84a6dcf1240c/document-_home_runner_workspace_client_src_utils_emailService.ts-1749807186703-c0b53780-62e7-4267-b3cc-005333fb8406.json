{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/emailService.ts"}, "originalCode": "// Email service utilities for form submissions and newsletter\n\nexport interface EmailServiceResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n}\n\nexport interface ContactFormSubmission {\n  name: string;\n  email: string;\n  company: string;\n  project: string;\n  budget: string;\n  message: string;\n}\n\nexport interface NewsletterSubmission {\n  email: string;\n}\n\nexport interface QuickQuoteSubmission {\n  service: string;\n  serviceOther: string;\n  websiteUrl: string;\n  description: string;\n  email: string;\n  phone: string;\n}\n\n// For production, you would replace this with actual email service integration\n// Options include: EmailJS, Formspree, Netlify Forms, or your own backend API\n\nexport const submitContactForm = async (data: ContactFormSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send contact form data to our backend API\n    const response = await fetch('/api/contact', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      // If MailerSend domain verification fails, provide helpful error message\n      if (result.message?.includes('verified domains')) {\n        throw new Error('Email service setup required. Please contact us <NAME_EMAIL> while we complete our email configuration.');\n      }\n      throw new Error(result.message || 'Failed to send message');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Thank you for your message! We\\'ll get back to you within 24 hours.'\n    };\n  } catch (error) {\n    console.error('Contact form submission error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\nexport const subscribeToNewsletter = async (data: NewsletterSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send newsletter subscription to our backend API\n    const response = await fetch('/api/newsletter', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      throw new Error(result.message || 'Failed to subscribe to newsletter');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Successfully subscribed! Check your email for confirmation.'\n    };\n  } catch (error) {\n    console.error('Newsletter subscription error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error with your subscription. Please try again.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\nexport const submitQuickQuoteForm = async (data: QuickQuoteSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send quick quote form data to our backend API\n    const response = await fetch('/api/quick-quote', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      throw new Error(result.message || 'Failed to submit quote request');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Thank you! We\\'ll get back to you with a quote within 1 business day.'\n    };\n  } catch (error) {\n    console.error('Quick quote submission error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error submitting your quote request. Please try again or contact us directly.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n", "modifiedCode": "// Email service utilities for form submissions and newsletter\n\nexport interface EmailServiceResponse {\n  success: boolean;\n  message: string;\n  error?: string;\n}\n\nexport interface ContactFormSubmission {\n  name: string;\n  email: string;\n  company: string;\n  project: string;\n  budget: string;\n  message: string;\n}\n\nexport interface NewsletterSubmission {\n  email: string;\n}\n\nexport interface QuickQuoteSubmission {\n  service: string;\n  serviceOther: string;\n  websiteUrl: string;\n  description: string;\n  email: string;\n  phone: string;\n}\n\n// For production, you would replace this with actual email service integration\n// Options include: EmailJS, Formspree, Netlify Forms, or your own backend API\n\nexport const submitContactForm = async (data: ContactFormSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send contact form data to our backend API\n    const response = await fetch('/api/contact', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      // If MailerSend domain verification fails, provide helpful error message\n      if (result.message?.includes('verified domains')) {\n        throw new Error('Email service setup required. Please contact us <NAME_EMAIL> while we complete our email configuration.');\n      }\n      throw new Error(result.message || 'Failed to send message');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Thank you for your message! We\\'ll get back to you within 24 hours.'\n    };\n  } catch (error) {\n    console.error('Contact form submission error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error sending your message. Please try again or contact us <NAME_EMAIL>.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\nexport const subscribeToNewsletter = async (data: NewsletterSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send newsletter subscription to our backend API\n    const response = await fetch('/api/newsletter', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      throw new Error(result.message || 'Failed to subscribe to newsletter');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Successfully subscribed! Check your email for confirmation.'\n    };\n  } catch (error) {\n    console.error('Newsletter subscription error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error with your subscription. Please try again.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n\nexport const submitQuickQuoteForm = async (data: QuickQuoteSubmission): Promise<EmailServiceResponse> => {\n  try {\n    // Send quick quote form data to our backend API\n    const response = await fetch('/api/quick-quote', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      throw new Error(result.message || 'Failed to submit quote request');\n    }\n\n    return {\n      success: true,\n      message: result.message || 'Thank you! We\\'ll get back to you with a quote within 1 business day.'\n    };\n  } catch (error) {\n    console.error('Quick quote submission error:', error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : 'Sorry, there was an error submitting your quote request. Please try again or contact us directly.',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    };\n  }\n};\n"}