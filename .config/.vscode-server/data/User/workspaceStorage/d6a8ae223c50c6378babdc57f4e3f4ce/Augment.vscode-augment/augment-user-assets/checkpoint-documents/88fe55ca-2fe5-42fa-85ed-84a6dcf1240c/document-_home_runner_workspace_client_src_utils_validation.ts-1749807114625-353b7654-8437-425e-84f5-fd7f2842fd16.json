{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/validation.ts"}, "originalCode": "// Form validation utilities\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: Record<string, string>;\n}\n\nexport interface ContactFormData {\n  name: string;\n  email: string;\n  company: string;\n  project: string;\n  budget: string;\n  message: string;\n}\n\nexport interface QuickQuoteFormData {\n  service: string;\n  serviceOther: string;\n  websiteUrl: string;\n  description: string;\n  email: string;\n  phone: string;\n}\n\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const validateContactForm = (data: ContactFormData): ValidationResult => {\n  const errors: Record<string, string> = {};\n\n  // Name validation\n  if (!data.name.trim()) {\n    errors.name = 'Name is required';\n  } else if (data.name.trim().length < 2) {\n    errors.name = 'Name must be at least 2 characters';\n  }\n\n  // Email validation\n  if (!data.email.trim()) {\n    errors.email = 'Email is required';\n  } else if (!validateEmail(data.email)) {\n    errors.email = 'Please enter a valid email address';\n  }\n\n  // Company validation (optional but if provided, should be valid)\n  if (data.company.trim() && data.company.trim().length < 2) {\n    errors.company = 'Company name must be at least 2 characters';\n  }\n\n  // Project type validation\n  if (!data.project) {\n    errors.project = 'Please select a project type';\n  }\n\n  // Budget validation\n  if (!data.budget) {\n    errors.budget = 'Please select a budget range';\n  }\n\n  // Message validation\n  if (!data.message.trim()) {\n    errors.message = 'Message is required';\n  } else if (data.message.trim().length < 10) {\n    errors.message = 'Message must be at least 10 characters';\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n\nexport const validateNewsletterEmail = (email: string): ValidationResult => {\n  const errors: Record<string, string> = {};\n\n  if (!email.trim()) {\n    errors.email = 'Email is required';\n  } else if (!validateEmail(email)) {\n    errors.email = 'Please enter a valid email address';\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n", "modifiedCode": "// Form validation utilities\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: Record<string, string>;\n}\n\nexport interface ContactFormData {\n  name: string;\n  email: string;\n  company: string;\n  project: string;\n  budget: string;\n  message: string;\n}\n\nexport interface QuickQuoteFormData {\n  service: string;\n  serviceOther: string;\n  websiteUrl: string;\n  description: string;\n  email: string;\n  phone: string;\n}\n\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\nexport const validateContactForm = (data: ContactFormData): ValidationResult => {\n  const errors: Record<string, string> = {};\n\n  // Name validation\n  if (!data.name.trim()) {\n    errors.name = 'Name is required';\n  } else if (data.name.trim().length < 2) {\n    errors.name = 'Name must be at least 2 characters';\n  }\n\n  // Email validation\n  if (!data.email.trim()) {\n    errors.email = 'Email is required';\n  } else if (!validateEmail(data.email)) {\n    errors.email = 'Please enter a valid email address';\n  }\n\n  // Company validation (optional but if provided, should be valid)\n  if (data.company.trim() && data.company.trim().length < 2) {\n    errors.company = 'Company name must be at least 2 characters';\n  }\n\n  // Project type validation\n  if (!data.project) {\n    errors.project = 'Please select a project type';\n  }\n\n  // Budget validation\n  if (!data.budget) {\n    errors.budget = 'Please select a budget range';\n  }\n\n  // Message validation\n  if (!data.message.trim()) {\n    errors.message = 'Message is required';\n  } else if (data.message.trim().length < 10) {\n    errors.message = 'Message must be at least 10 characters';\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n\nexport const validateNewsletterEmail = (email: string): ValidationResult => {\n  const errors: Record<string, string> = {};\n\n  if (!email.trim()) {\n    errors.email = 'Email is required';\n  } else if (!validateEmail(email)) {\n    errors.email = 'Please enter a valid email address';\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors\n  };\n};\n"}