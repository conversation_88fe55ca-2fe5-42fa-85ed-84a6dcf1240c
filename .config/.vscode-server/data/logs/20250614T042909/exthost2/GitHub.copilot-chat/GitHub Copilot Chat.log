2025-06-14 04:33:34.484 [info] Can't use the Electron fetcher in this environment.
2025-06-14 04:33:34.485 [info] Using the Node fetch fetcher.
2025-06-14 04:33:34.485 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-14 04:33:34.485 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-14 04:33:34.485 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-14 04:33:34.485 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-14 04:33:36.794 [info] Logged in as edwardbowman_nbnco
2025-06-14 04:33:37.714 [info] Got Copilot token for edwardbowman_nbnco
2025-06-14 04:33:37.721 [info] activationBlocker from 'languageModelAccess' took for 3280ms
2025-06-14 04:33:38.237 [info] Fetched model metadata in 516ms 47fadfdf-ad48-4c3e-b161-052a7672b609
2025-06-14 04:33:38.938 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat
2025-06-14 04:33:38.954 [info] Registering default platform agent...
2025-06-14 04:33:38.954 [info] activationBlocker from 'conversationFeature' took for 4519ms
2025-06-14 04:33:38.955 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-14 04:33:38.955 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-14 04:33:38.955 [info] Successfully registered GitHub PR title and description provider.
2025-06-14 04:33:38.955 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-14 04:33:39.764 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
2025-06-14 04:33:40.009 [info] Fetched content exclusion rules in 845ms
2025-06-14 04:33:41.018 [info] Fetched content exclusion rules in 1009ms
2025-06-14 04:33:41.029 [info] Fetched content exclusion rules in 1020ms
