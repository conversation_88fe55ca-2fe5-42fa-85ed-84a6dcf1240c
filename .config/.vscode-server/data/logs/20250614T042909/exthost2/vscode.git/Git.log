2025-06-14 04:33:33.506 [info] [main] Log level: Info
2025-06-14 04:33:33.506 [info] [main] Validating found git in: "git"
2025-06-14 04:33:33.506 [info] [main] Using git "2.47.2" from "git"
2025-06-14 04:33:33.506 [info] [Model][doInitialScan] Initial repository scan started
2025-06-14 04:33:33.506 [info] > git rev-parse --show-toplevel [4ms]
2025-06-14 04:33:33.506 [info] > git rev-parse --git-dir --git-common-dir [2937ms]
2025-06-14 04:33:33.506 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-14 04:33:33.506 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-14 04:33:33.506 [info] > git rev-parse --show-toplevel [10ms]
2025-06-14 04:33:33.506 [info] > git config --get commit.template [15ms]
2025-06-14 04:33:33.506 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [136ms]
2025-06-14 04:33:33.506 [info] > git rev-parse --show-toplevel [132ms]
2025-06-14 04:33:33.560 [info] > git status -z -uall [6ms]
2025-06-14 04:33:33.560 [info] > git rev-parse --show-toplevel [48ms]
2025-06-14 04:33:33.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-14 04:33:34.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [777ms]
2025-06-14 04:33:34.366 [info] > git rev-parse --show-toplevel [802ms]
2025-06-14 04:33:34.389 [info] > git check-ignore -v -z --stdin [17ms]
2025-06-14 04:33:34.390 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-06-14 04:33:34.395 [info] > git config --get commit.template [267ms]
2025-06-14 04:33:34.398 [info] > git rev-parse --show-toplevel [22ms]
2025-06-14 04:33:34.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [12ms]
2025-06-14 04:33:34.412 [info] > git rev-parse --show-toplevel [6ms]
2025-06-14 04:33:34.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-14 04:33:34.416 [info] > git merge-base refs/heads/main refs/remotes/origin/main [6ms]
2025-06-14 04:33:34.458 [info] > git rev-parse --show-toplevel [42ms]
2025-06-14 04:33:34.472 [info] > git diff --name-status -z --diff-filter=ADMR a9760fde6fc081c302411d62a5d4c72b5a1424fd...refs/remotes/origin/main [52ms]
2025-06-14 04:33:34.485 [info] > git status -z -uall [6ms]
2025-06-14 04:33:34.485 [info] > git rev-parse --show-toplevel [20ms]
2025-06-14 04:33:34.491 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-14 04:33:34.494 [info] > git rev-parse --show-toplevel [4ms]
2025-06-14 04:33:34.501 [info] > git rev-parse --show-toplevel [2ms]
2025-06-14 04:33:34.503 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-14 04:33:34.623 [info] > git show --textconv :server/csvLogger.ts [12ms]
2025-06-14 04:33:34.624 [info] > git ls-files --stage -- server/csvLogger.ts [10ms]
2025-06-14 04:33:34.649 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [18ms]
2025-06-14 04:33:35.788 [info] > git config --get commit.template [6ms]
2025-06-14 04:33:35.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:33:35.805 [info] > git status -z -uall [5ms]
2025-06-14 04:33:35.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:37.313 [info] > git config --get --local branch.main.github-pr-owner-number [149ms]
2025-06-14 04:33:37.313 [warning] [Git][config] git config failed: Failed to execute git
2025-06-14 04:33:47.996 [info] > git fetch [248ms]
2025-06-14 04:33:47.997 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-14 04:33:48.009 [info] > git config --get commit.template [7ms]
2025-06-14 04:33:48.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:48.024 [info] > git status -z -uall [8ms]
2025-06-14 04:33:48.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 04:33:48.045 [info] > git config --get commit.template [7ms]
2025-06-14 04:33:48.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:48.071 [info] > git status -z -uall [12ms]
2025-06-14 04:33:48.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:49.410 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-14 04:33:49.415 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [2ms]
2025-06-14 04:33:49.647 [info] > git show --textconv :server/csvLogger.ts [1ms]
2025-06-14 04:33:53.092 [info] > git config --get commit.template [8ms]
2025-06-14 04:33:53.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:33:53.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:33:53.109 [info] > git status -z -uall [8ms]
2025-06-14 04:33:58.121 [info] > git config --get commit.template [4ms]
2025-06-14 04:33:58.122 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:33:58.130 [info] > git status -z -uall [4ms]
2025-06-14 04:33:58.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:34:03.144 [info] > git config --get commit.template [5ms]
2025-06-14 04:34:03.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:03.153 [info] > git status -z -uall [4ms]
2025-06-14 04:34:03.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:08.835 [info] > git config --get commit.template [5ms]
2025-06-14 04:34:08.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:34:08.845 [info] > git status -z -uall [4ms]
2025-06-14 04:34:08.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:13.862 [info] > git config --get commit.template [6ms]
2025-06-14 04:34:13.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:13.873 [info] > git status -z -uall [6ms]
2025-06-14 04:34:13.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:18.886 [info] > git config --get commit.template [4ms]
2025-06-14 04:34:18.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:18.896 [info] > git status -z -uall [5ms]
2025-06-14 04:34:18.897 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:34:23.910 [info] > git config --get commit.template [4ms]
2025-06-14 04:34:23.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:34:23.918 [info] > git status -z -uall [4ms]
2025-06-14 04:34:23.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:28.930 [info] > git config --get commit.template [3ms]
2025-06-14 04:34:28.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:28.944 [info] > git status -z -uall [4ms]
2025-06-14 04:34:28.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:33.957 [info] > git config --get commit.template [4ms]
2025-06-14 04:34:33.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:33.966 [info] > git status -z -uall [5ms]
2025-06-14 04:34:33.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:34:38.979 [info] > git config --get commit.template [5ms]
2025-06-14 04:34:38.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:38.987 [info] > git status -z -uall [3ms]
2025-06-14 04:34:38.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:34:43.998 [info] > git config --get commit.template [1ms]
2025-06-14 04:34:44.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:34:44.013 [info] > git status -z -uall [4ms]
2025-06-14 04:34:44.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:36:45.680 [info] > git config --get commit.template [4ms]
2025-06-14 04:36:45.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:36:45.689 [info] > git status -z -uall [5ms]
2025-06-14 04:36:45.689 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:36:50.701 [info] > git config --get commit.template [4ms]
2025-06-14 04:36:50.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:36:50.711 [info] > git status -z -uall [5ms]
2025-06-14 04:36:50.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:36:51.656 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-14 04:36:53.460 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-14 04:36:55.733 [info] > git config --get commit.template [6ms]
2025-06-14 04:36:55.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-14 04:36:55.750 [info] > git status -z -uall [10ms]
2025-06-14 04:36:55.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:37:00.763 [info] > git config --get commit.template [4ms]
2025-06-14 04:37:00.764 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:00.773 [info] > git status -z -uall [5ms]
2025-06-14 04:37:00.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:05.788 [info] > git config --get commit.template [6ms]
2025-06-14 04:37:05.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:05.803 [info] > git status -z -uall [7ms]
2025-06-14 04:37:05.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-14 04:37:10.861 [info] > git config --get commit.template [5ms]
2025-06-14 04:37:10.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:10.870 [info] > git status -z -uall [4ms]
2025-06-14 04:37:10.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:15.884 [info] > git config --get commit.template [5ms]
2025-06-14 04:37:15.885 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:37:15.895 [info] > git status -z -uall [6ms]
2025-06-14 04:37:15.896 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:20.907 [info] > git config --get commit.template [4ms]
2025-06-14 04:37:20.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:20.918 [info] > git status -z -uall [5ms]
2025-06-14 04:37:20.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:37:25.940 [info] > git config --get commit.template [6ms]
2025-06-14 04:37:25.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:25.948 [info] > git status -z -uall [3ms]
2025-06-14 04:37:25.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:37:30.962 [info] > git config --get commit.template [4ms]
2025-06-14 04:37:30.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:30.970 [info] > git status -z -uall [4ms]
2025-06-14 04:37:30.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:35.981 [info] > git config --get commit.template [1ms]
2025-06-14 04:37:35.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:35.993 [info] > git status -z -uall [3ms]
2025-06-14 04:37:35.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:52.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-14 04:37:52.035 [info] > git config --get commit.template [10ms]
2025-06-14 04:37:52.048 [info] > git status -z -uall [7ms]
2025-06-14 04:37:52.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:37:57.060 [info] > git config --get commit.template [3ms]
2025-06-14 04:37:57.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:37:57.070 [info] > git status -z -uall [5ms]
2025-06-14 04:37:57.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:02.084 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:02.085 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:02.094 [info] > git status -z -uall [5ms]
2025-06-14 04:38:02.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:09.828 [info] > git config --get commit.template [2ms]
2025-06-14 04:38:09.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:09.843 [info] > git status -z -uall [5ms]
2025-06-14 04:38:09.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:14.858 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:14.860 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:14.869 [info] > git status -z -uall [6ms]
2025-06-14 04:38:14.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:19.887 [info] > git config --get commit.template [7ms]
2025-06-14 04:38:19.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:19.898 [info] > git status -z -uall [5ms]
2025-06-14 04:38:19.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:24.915 [info] > git config --get commit.template [5ms]
2025-06-14 04:38:24.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:24.924 [info] > git status -z -uall [3ms]
2025-06-14 04:38:24.925 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:29.940 [info] > git config --get commit.template [4ms]
2025-06-14 04:38:29.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:29.949 [info] > git status -z -uall [4ms]
2025-06-14 04:38:29.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-14 04:38:34.962 [info] > git config --get commit.template [2ms]
2025-06-14 04:38:34.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-14 04:38:34.974 [info] > git status -z -uall [4ms]
2025-06-14 04:38:34.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-14 04:38:39.989 [info] > git config --get commit.template [5ms]
2025-06-14 04:38:39.990 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-14 04:38:39.999 [info] > git status -z -uall [4ms]
2025-06-14 04:38:40.000 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
