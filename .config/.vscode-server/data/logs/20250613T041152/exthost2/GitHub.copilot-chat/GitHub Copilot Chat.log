2025-06-13 09:03:55.503 [info] Can't use the Electron fetcher in this environment.
2025-06-13 09:03:55.503 [info] Using the Node fetch fetcher.
2025-06-13 09:03:55.503 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-13 09:03:55.503 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-13 09:03:55.503 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-13 09:03:55.503 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-13 09:03:57.042 [info] Logged in as edwardbowman_nbnco
2025-06-13 09:03:58.016 [info] Got Copilot token for edwardbowman_nbnco
2025-06-13 09:03:58.023 [info] activationBlocker from 'languageModelAccess' took for 2611ms
2025-06-13 09:03:58.513 [info] Fetched model metadata in 490ms b65c2e1c-cfc9-4783-87e2-d7ae7f21c38b
2025-06-13 09:03:59.667 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat
2025-06-13 09:03:59.682 [info] Registering default platform agent...
2025-06-13 09:03:59.683 [info] activationBlocker from 'conversationFeature' took for 4275ms
2025-06-13 09:03:59.685 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-13 09:03:59.685 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-13 09:03:59.685 [info] Successfully registered GitHub PR title and description provider.
2025-06-13 09:03:59.685 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-13 09:04:00.484 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
2025-06-13 09:04:00.715 [info] Fetched content exclusion rules in 823ms
2025-06-13 09:04:01.786 [info] Fetched content exclusion rules in 1070ms
2025-06-13 09:04:01.823 [info] Fetched content exclusion rules in 1106ms
