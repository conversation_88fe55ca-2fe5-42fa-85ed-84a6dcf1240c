2025-06-13 09:03:55.150 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 09:03:55.150 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"I am using VScode on a macbook. Everytime we make changes please make sure my augment-guidelines.md gets updated to reflect the correct way to do things I want to make sure we always maintain a consistent set of standards that we use for everything we do. Please ensure you search the standards before updating so that we can ensure there is no duplication and a clean layout of rules and standards is always maintained."},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 09:03:55.150 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":""}
2025-06-13 09:04:19.857 [info] 'OAuthFlow' Creating new session...
2025-06-13 09:04:20.055 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=t2nRIhhKWr1WArQhvPv6loO_NaMXs_syP9NUyLoJF6o&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=2c877901-b29c-4192-b981-70f7ba16b3e0&scope=email&prompt=login
2025-06-13 09:04:20.480 [info] 'OAuthFlow' Creating new session...
2025-06-13 09:04:20.698 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=WFmlJrBZ7IUFQjRqEChfkJkCRaHCcPKt1uS0OQ378Dw&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=3a38e85e-b717-4568-81a6-a8245c0ada05&scope=email&prompt=login
2025-06-13 09:04:47.279 [warning] 'OAuthFlow' Failed to process auth request: No OAuth state found
2025-06-13 09:06:26.172 [info] 'OAuthFlow' Creating new session...
2025-06-13 09:06:26.372 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=ENpgkXY4UJtGtqQylGdUdFfUE6o-yGjMAip0LFEuEM4&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=b9138b75-f81a-4d10-9a67-cc5f5a021eea&scope=email&prompt=login
2025-06-13 09:06:39.276 [info] 'activate()' ======== Reloading extension ========
2025-06-13 09:06:39.470 [info] 'AugmentExtension' Retrieving model config
2025-06-13 09:06:39.471 [info] 'OAuthFlow' Created session https://i0.api.augmentcode.com/
2025-06-13 09:06:39.724 [info] 'AugmentExtension' Retrieved model config
2025-06-13 09:06:39.725 [info] 'AugmentExtension' Returning model config
2025-06-13 09:06:39.754 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.449.0"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-13 09:06:39.754 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/13/2025, 1:20:03 AM
2025-06-13 09:06:39.754 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-13 09:06:39.754 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 09:06:39.754 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/13/2025, 1:20:03 AM; type = explicit
2025-06-13 09:06:39.754 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-13 09:06:39.754 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/13/2025, 1:20:03 AM
2025-06-13 09:06:39.794 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 09:06:39.794 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 09:06:39.794 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 09:06:39.801 [info] 'ToolsModel' Tools Mode: undefined (0 hosts)
2025-06-13 09:06:40.415 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 09:06:40.416 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 09:06:40.416 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-13 09:06:40.416 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 09:06:40.416 [info] 'TaskManager' Setting current root task UUID to 4fa79aeb-a20a-45c7-97df-01eb941a5c99
2025-06-13 09:06:40.417 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 09:06:40.646 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-13 09:06:40.650 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-13 09:06:40.650 [info] 'OpenFileManager' Opened source folder 100
2025-06-13 09:06:40.651 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-13 09:06:40.654 [info] 'MtimeCache[workspace]' read 1503 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-13 09:06:41.210 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-13 09:06:41.210 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:06:41.210 [info] 'TaskManager' Setting current root task UUID to 0da89532-9c4b-4614-b2f1-06a488c36390
2025-06-13 09:06:41.210 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 09:06:41.242 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:06:41.301 [info] 'ToolsModel' Host: remoteToolHost (2 tools: 27 enabled, 0 disabled})
 + web-search
 + github-api

2025-06-13 09:06:41.301 [info] 'ToolsModel' Host: sidecarToolHost (9 tools: 149 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-13 09:06:41.475 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document--1749788817585-74e4f5ce-35be-4f47-a647-d758470a1ba3.json
2025-06-13 09:06:41.476 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document--1749788817585-74e4f5ce-35be-4f47-a647-d758470a1ba3.json
2025-06-13 09:07:04.927 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-13 09:07:04.927 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 393
  - files emitted: 1649
  - other paths emitted: 5
  - total paths emitted: 2047
  - timing stats:
    - readDir: 17 ms
    - filter: 142 ms
    - yield: 22 ms
    - total: 192 ms
2025-06-13 09:07:04.927 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1555
  - paths not accessible: 0
  - not plain files: 0
  - large files: 23
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1492
  - mtime cache misses: 63
  - probe batches: 9
  - blob names probed: 1622
  - files read: 200
  - blobs uploaded: 31
  - timing stats:
    - ingestPath: 12 ms
    - probe: 4754 ms
    - stat: 29 ms
    - read: 820 ms
    - upload: 1079 ms
2025-06-13 09:07:04.927 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 5 ms
  - read MtimeCache: 3 ms
  - pre-populate PathMap: 36 ms
  - create PathFilter: 86 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 201 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 23946 ms
  - enable persist: 2 ms
  - total: 24280 ms
2025-06-13 09:07:04.927 [info] 'WorkspaceManager' Workspace startup complete in 25183 ms
2025-06-13 09:07:22.877 [info] 'PathMap' Closed source folder /home/<USER>/workspace with id 100
2025-06-13 09:07:22.877 [info] 'OpenFileManager' Closed source folder 100
2025-06-13 09:07:22.879 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unknown" due to error: Canceled
