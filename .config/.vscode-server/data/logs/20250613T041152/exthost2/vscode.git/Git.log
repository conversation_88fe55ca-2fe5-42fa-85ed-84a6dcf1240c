2025-06-13 09:03:54.082 [info] [main] Log level: Info
2025-06-13 09:03:54.082 [info] [main] Validating found git in: "git"
2025-06-13 09:03:54.082 [info] [main] Using git "2.47.2" from "git"
2025-06-13 09:03:54.082 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 09:03:54.082 [info] > git rev-parse --show-toplevel [13ms]
2025-06-13 09:03:54.082 [info] > git rev-parse --git-dir --git-common-dir [3077ms]
2025-06-13 09:03:54.082 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-13 09:03:54.082 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-13 09:03:54.082 [info] > git config --get commit.template [36ms]
2025-06-13 09:03:54.082 [info] > git rev-parse --show-toplevel [65ms]
2025-06-13 09:03:54.082 [info] > git rev-parse --show-toplevel [243ms]
2025-06-13 09:03:54.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [660ms]
2025-06-13 09:03:54.141 [info] > git rev-parse --show-toplevel [33ms]
2025-06-13 09:03:54.303 [info] > git status -z -uall [64ms]
2025-06-13 09:03:54.303 [info] > git rev-parse --show-toplevel [113ms]
2025-06-13 09:03:55.134 [info] > git fetch [1816ms]
2025-06-13 09:03:55.134 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-13 09:03:55.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [835ms] (cancelled)
2025-06-13 09:03:55.184 [info] > git check-ignore -v -z --stdin [36ms]
2025-06-13 09:03:55.186 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [44ms]
2025-06-13 09:03:55.222 [info] > git rev-parse --show-toplevel [898ms]
2025-06-13 09:03:55.426 [info] > git config --get --local branch.main.vscode-merge-base [205ms]
2025-06-13 09:03:55.435 [info] > git config --get commit.template [268ms]
2025-06-13 09:03:55.435 [info] > git config --get commit.template [273ms]
2025-06-13 09:03:55.438 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [4ms]
2025-06-13 09:03:55.450 [info] > git rev-parse --show-toplevel [62ms]
2025-06-13 09:03:55.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-13 09:03:55.481 [info] > git merge-base refs/heads/main refs/remotes/origin/main [31ms]
2025-06-13 09:03:55.492 [info] > git rev-parse --show-toplevel [13ms]
2025-06-13 09:03:55.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-06-13 09:03:55.502 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [12ms]
2025-06-13 09:03:55.608 [info] > git rev-parse --show-toplevel [107ms]
2025-06-13 09:03:55.629 [info] > git rev-parse --show-toplevel [12ms]
2025-06-13 09:03:55.631 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 09:03:55.649 [info] > git status -z -uall [26ms]
2025-06-13 09:03:55.649 [info] > git show --textconv :server/csvLogger.ts [13ms]
2025-06-13 09:03:55.649 [info] > git ls-files --stage -- server/csvLogger.ts [9ms]
2025-06-13 09:03:55.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [26ms]
2025-06-13 09:03:55.964 [info] > git cat-file -s 563bb11ecd72c4f876000ef8becf4c1b5e588986 [311ms]
2025-06-13 09:03:56.454 [info] > git config --get commit.template [5ms]
2025-06-13 09:03:56.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:03:56.463 [info] > git status -z -uall [4ms]
2025-06-13 09:03:56.463 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:03:57.581 [info] > git config --get --local branch.main.github-pr-owner-number [132ms]
2025-06-13 09:03:57.581 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:04:19.858 [info] > git config --get commit.template [3ms]
2025-06-13 09:04:19.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:04:19.879 [info] > git status -z -uall [4ms]
2025-06-13 09:04:19.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:04:47.107 [info] > git config --get commit.template [1ms]
2025-06-13 09:04:47.118 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:04:47.134 [info] > git status -z -uall [7ms]
2025-06-13 09:04:47.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:06:25.726 [info] > git config --get commit.template [4ms]
2025-06-13 09:06:25.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:06:25.735 [info] > git status -z -uall [4ms]
2025-06-13 09:06:25.736 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:06:38.004 [info] > git config --get commit.template [7ms]
2025-06-13 09:06:38.006 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:06:38.022 [info] > git status -z -uall [6ms]
2025-06-13 09:06:38.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:06:43.065 [info] > git config --get commit.template [13ms]
2025-06-13 09:06:43.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:06:43.090 [info] > git status -z -uall [14ms]
2025-06-13 09:06:43.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 09:07:16.801 [info] > git config --get commit.template [9ms]
2025-06-13 09:07:16.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:07:16.816 [info] > git status -z -uall [7ms]
2025-06-13 09:07:16.817 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:07:21.847 [info] > git config --get commit.template [10ms]
2025-06-13 09:07:21.848 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:07:21.868 [info] > git status -z -uall [13ms]
2025-06-13 09:07:21.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
