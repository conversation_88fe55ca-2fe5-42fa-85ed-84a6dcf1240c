2025-06-13 09:03:55.980 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-13 09:03:55.980 [info] [Activation] Extension version: 0.111.**********
2025-06-13 09:03:56.620 [info] [Authentication] Creating hub for .com
2025-06-13 09:03:57.096 [info] [Activation] Looking for git repository
2025-06-13 09:03:57.096 [info] [Activation] Found 0 repositories during activation
2025-06-13 09:03:57.096 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-13 09:03:57.100 [info] [GitAPI] Registering git provider
2025-06-13 09:03:57.100 [info] [Review+0] Validate state in progress
2025-06-13 09:03:57.100 [info] [Review+0] Validating state...
2025-06-13 09:03:57.320 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:03:57.439 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:03:57.439 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:03:57.441 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-13 09:03:57.449 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:03:57.454 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:03:57.456 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:03:57.582 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:03:57.827 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:03:57.827 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:03:57.982 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-13 09:03:58.283 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:04:19.882 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-13 09:04:19.882 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-13 09:05:57.100 [error] [Review+0] Timeout occurred while validating state.
