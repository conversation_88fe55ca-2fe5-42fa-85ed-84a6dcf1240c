2025-06-13 04:11:52.411 [info] 




2025-06-13 04:11:52.420 [info] Extension host agent started.
2025-06-13 04:11:53.079 [error] [<unknown>][272a4168][ManagementConnection] Unknown reconnection token (never seen).
2025-06-13 04:12:01.217 [info] [<unknown>][7c560750][ExtensionHostConnection] New connection established.
2025-06-13 04:12:01.218 [info] [<unknown>][66fd4738][ManagementConnection] New connection established.
2025-06-13 04:12:01.222 [info] [<unknown>][7c560750][ExtensionHostConnection] <14636> Launched Extension Host Process.
2025-06-13 04:12:01.742 [info] ComputeTargetPlatform: linux-x64
2025-06-13 04:12:06.732 [info] ComputeTargetPlatform: linux-x64
2025-06-13 04:16:52.421 [info] New EH opened, aborting shutdown
2025-06-13 04:26:57.909 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-13 09:03:22.969 [info] [<unknown>][66fd4738][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-13 09:03:23.324 [info] [<unknown>][7c560750][ExtensionHostConnection] <14636> Extension Host Process exited with code: 0, signal: null.
2025-06-13 09:03:23.327 [info] Last EH closed, waiting before shutting down
2025-06-13 09:03:46.146 [info] [<unknown>][4c3c3360][ManagementConnection] New connection established.
2025-06-13 09:03:46.147 [info] [<unknown>][2f84f3fe][ExtensionHostConnection] New connection established.
2025-06-13 09:03:46.173 [info] [<unknown>][2f84f3fe][ExtensionHostConnection] <27214> Launched Extension Host Process.
2025-06-13 09:03:51.228 [info] Getting Manifest... github.copilot
2025-06-13 09:03:51.330 [info] Installing extension: github.copilot {"productVersion":{"version":"1.101.0","date":"2025-06-11T15:00:50.123Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-13 09:03:53.163 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1212ms.
2025-06-13 09:03:54.517 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.335.0: github.copilot
2025-06-13 09:03:54.543 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.335.0
2025-06-13 09:03:54.573 [info] Marked extension as removed github.copilot-1.333.0
2025-06-13 09:03:54.584 [info] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-13 09:07:22.800 [info] [<unknown>][4c3c3360][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-13 09:07:22.981 [info] [<unknown>][2f84f3fe][ExtensionHostConnection] <27214> Extension Host Process exited with code: 0, signal: null.
2025-06-13 09:07:22.981 [info] Cancelling previous shutdown timeout
2025-06-13 09:07:22.981 [info] Last EH closed, waiting before shutting down
2025-06-13 09:07:24.979 [info] [<unknown>][ed5cc218][ManagementConnection] New connection established.
2025-06-13 09:07:24.979 [info] [<unknown>][5c9d707e][ExtensionHostConnection] New connection established.
2025-06-13 09:07:24.984 [info] [<unknown>][5c9d707e][ExtensionHostConnection] <27916> Launched Extension Host Process.
2025-06-13 09:12:22.982 [info] New EH opened, aborting shutdown
2025-06-13 09:12:22.985 [error] Error: Unexpected SIGPIPE
    at process.<anonymous> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/server-main.js:191:1060)
    at process.emit (node:events:530:35)
2025-06-13 09:39:50.217 [error] Error: connect ECONNREFUSED 127.0.0.1:5173
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-13 09:43:12.219 [warning] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
2025-06-13 09:45:50.345 [error] Error: connect ECONNREFUSED 127.0.0.1:5173
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
2025-06-13 09:45:54.367 [error] Error: connect ECONNREFUSED 127.0.0.1:9229
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
