2025-06-13 09:07:33.410 [info] Can't use the Electron fetcher in this environment.
2025-06-13 09:07:33.410 [info] Using the Node fetch fetcher.
2025-06-13 09:07:33.410 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-13 09:07:33.410 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-13 09:07:33.410 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-13 09:07:33.410 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-13 09:07:36.085 [info] Logged in as edwardbowman_nbnco
2025-06-13 09:07:36.831 [info] Got Copilot token for edwardbowman_nbnco
2025-06-13 09:07:36.837 [info] activationBlocker from 'languageModelAccess' took for 5364ms
2025-06-13 09:07:37.833 [info] Fetched model metadata in 996ms d3b0589c-fce9-4763-9b12-86b0c27881a0
2025-06-13 09:07:38.566 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat
2025-06-13 09:07:38.587 [info] Registering default platform agent...
2025-06-13 09:07:38.588 [info] activationBlocker from 'conversationFeature' took for 7128ms
2025-06-13 09:07:38.589 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-13 09:07:38.589 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-13 09:07:38.589 [info] Successfully registered GitHub PR title and description provider.
2025-06-13 09:07:38.589 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-13 09:07:39.396 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
2025-06-13 09:07:39.643 [info] Fetched content exclusion rules in 836ms
