2025-06-13 09:07:31.427 [info] [main] Log level: Info
2025-06-13 09:07:31.427 [info] [main] Validating found git in: "git"
2025-06-13 09:07:31.427 [info] [main] Using git "2.47.2" from "git"
2025-06-13 09:07:31.427 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 09:07:31.427 [info] > git rev-parse --show-toplevel [508ms]
2025-06-13 09:07:31.427 [info] > git rev-parse --git-dir --git-common-dir [2146ms]
2025-06-13 09:07:31.427 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-13 09:07:31.427 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-13 09:07:31.427 [info] > git rev-parse --show-toplevel [97ms]
2025-06-13 09:07:31.427 [info] > git config --get commit.template [99ms]
2025-06-13 09:07:31.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [520ms]
2025-06-13 09:07:31.948 [info] > git rev-parse --show-toplevel [4ms]
2025-06-13 09:07:31.949 [info] > git fetch [659ms]
2025-06-13 09:07:31.949 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-13 09:07:32.072 [info] > git config --get commit.template [117ms]
2025-06-13 09:07:32.072 [info] > git rev-parse --show-toplevel [120ms]
2025-06-13 09:07:32.118 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [38ms]
2025-06-13 09:07:32.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-13 09:07:32.133 [info] > git config --get commit.template [31ms]
2025-06-13 09:07:32.133 [info] > git rev-parse --show-toplevel [37ms]
2025-06-13 09:07:33.149 [info] > git config --get --local branch.main.vscode-merge-base [1024ms]
2025-06-13 09:07:33.388 [info] > git check-ignore -v -z --stdin [207ms]
2025-06-13 09:07:33.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [230ms]
2025-06-13 09:07:33.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [210ms]
2025-06-13 09:07:33.409 [info] > git rev-parse --show-toplevel [218ms]
2025-06-13 09:07:33.417 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-06-13 09:07:33.441 [info] > git rev-parse --show-toplevel [25ms]
2025-06-13 09:07:33.452 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [30ms]
2025-06-13 09:07:33.762 [info] > git status -z -uall [11ms]
2025-06-13 09:07:33.763 [info] > git merge-base refs/heads/main refs/remotes/origin/main [335ms]
2025-06-13 09:07:34.191 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [420ms]
2025-06-13 09:07:34.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [432ms]
2025-06-13 09:07:34.199 [info] > git rev-parse --show-toplevel [750ms]
2025-06-13 09:07:34.726 [info] > git rev-parse --show-toplevel [404ms]
2025-06-13 09:07:35.221 [info] > git rev-parse --show-toplevel [49ms]
2025-06-13 09:07:35.223 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 09:07:35.303 [info] > git show --textconv :server/csvLogger.ts [64ms]
2025-06-13 09:07:35.333 [info] > git ls-files --stage -- server/csvLogger.ts [86ms]
2025-06-13 09:07:35.343 [info] > git cat-file -s 563bb11ecd72c4f876000ef8becf4c1b5e588986 [3ms]
2025-06-13 09:07:35.490 [info] > git config --get commit.template [12ms]
2025-06-13 09:07:35.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [96ms]
2025-06-13 09:07:35.671 [info] > git status -z -uall [14ms]
2025-06-13 09:07:35.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [180ms]
2025-06-13 09:07:37.106 [info] > git config --get --local branch.main.github-pr-owner-number [154ms]
2025-06-13 09:07:37.106 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:09:18.132 [info] > git config --get commit.template [9ms]
2025-06-13 09:09:18.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:18.142 [info] > git status -z -uall [4ms]
2025-06-13 09:09:18.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:09:18.351 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:09:18.361 [info] > git cat-file -s 563bb11ecd72c4f876000ef8becf4c1b5e588986 [2ms]
2025-06-13 09:09:18.556 [info] > git show --textconv :server/csvLogger.ts [1ms]
2025-06-13 09:09:23.188 [info] > git config --get commit.template [11ms]
2025-06-13 09:09:23.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:09:23.251 [info] > git status -z -uall [37ms]
2025-06-13 09:09:23.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [35ms]
2025-06-13 09:09:28.299 [info] > git config --get commit.template [8ms]
2025-06-13 09:09:28.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:28.312 [info] > git status -z -uall [5ms]
2025-06-13 09:09:28.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:09:38.583 [info] > git config --get commit.template [22ms]
2025-06-13 09:09:38.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:09:38.612 [info] > git status -z -uall [8ms]
2025-06-13 09:09:38.616 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:09:43.630 [info] > git config --get commit.template [4ms]
2025-06-13 09:09:43.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:43.639 [info] > git status -z -uall [3ms]
2025-06-13 09:09:43.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:09:48.659 [info] > git config --get commit.template [7ms]
2025-06-13 09:09:48.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:48.671 [info] > git status -z -uall [4ms]
2025-06-13 09:09:48.674 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:09:53.699 [info] > git config --get commit.template [13ms]
2025-06-13 09:09:53.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:09:53.715 [info] > git status -z -uall [9ms]
2025-06-13 09:09:53.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 09:09:58.737 [info] > git config --get commit.template [8ms]
2025-06-13 09:09:58.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:58.751 [info] > git status -z -uall [7ms]
2025-06-13 09:09:58.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:10:03.773 [info] > git config --get commit.template [12ms]
2025-06-13 09:10:03.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:10:03.790 [info] > git status -z -uall [7ms]
2025-06-13 09:10:03.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:10:08.804 [info] > git config --get commit.template [4ms]
2025-06-13 09:10:08.805 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:10:08.819 [info] > git status -z -uall [6ms]
2025-06-13 09:10:08.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:16:17.469 [info] > git config --get commit.template [16ms]
2025-06-13 09:16:17.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:16:17.486 [info] > git status -z -uall [7ms]
2025-06-13 09:16:17.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:16:22.505 [info] > git config --get commit.template [6ms]
2025-06-13 09:16:22.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:16:22.520 [info] > git status -z -uall [6ms]
2025-06-13 09:16:22.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:16:27.535 [info] > git config --get commit.template [0ms]
2025-06-13 09:16:27.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:16:27.557 [info] > git status -z -uall [9ms]
2025-06-13 09:16:27.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:16:32.571 [info] > git config --get commit.template [6ms]
2025-06-13 09:16:32.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:16:32.586 [info] > git status -z -uall [6ms]
2025-06-13 09:16:32.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:17:25.786 [info] > git config --get commit.template [8ms]
2025-06-13 09:17:25.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:17:25.799 [info] > git status -z -uall [6ms]
2025-06-13 09:17:25.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:17:57.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:17:57.430 [info] > git config --get commit.template [9ms]
2025-06-13 09:17:57.443 [info] > git status -z -uall [7ms]
2025-06-13 09:17:57.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:17:57.579 [info] > git merge-base refs/heads/main refs/remotes/origin/main [126ms]
2025-06-13 09:17:57.588 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:17:57.654 [info] > git ls-files --stage -- server/csvLogger.ts [1ms]
2025-06-13 09:17:57.659 [info] > git cat-file -s 87ec41b383c9b9a3d63def2c3f40cafd19a4f5fa [2ms]
2025-06-13 09:17:57.814 [info] > git config --get --local branch.main.github-pr-owner-number [3ms]
2025-06-13 09:17:57.814 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:17:57.895 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:18:02.474 [info] > git config --get commit.template [3ms]
2025-06-13 09:18:02.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:18:02.497 [info] > git status -z -uall [6ms]
2025-06-13 09:18:02.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:23.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:18:23.817 [info] > git config --get commit.template [17ms]
2025-06-13 09:18:23.846 [info] > git status -z -uall [12ms]
2025-06-13 09:18:23.847 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:18:28.857 [info] > git config --get commit.template [2ms]
2025-06-13 09:18:28.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:28.877 [info] > git status -z -uall [7ms]
2025-06-13 09:18:28.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:33.890 [info] > git config --get commit.template [1ms]
2025-06-13 09:18:33.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:33.910 [info] > git status -z -uall [5ms]
2025-06-13 09:18:33.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:38.924 [info] > git config --get commit.template [2ms]
2025-06-13 09:18:38.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:38.953 [info] > git status -z -uall [10ms]
2025-06-13 09:18:38.955 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:18:43.971 [info] > git config --get commit.template [6ms]
2025-06-13 09:18:43.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:43.984 [info] > git status -z -uall [7ms]
2025-06-13 09:18:43.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:18:48.998 [info] > git config --get commit.template [4ms]
2025-06-13 09:18:48.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:18:49.016 [info] > git status -z -uall [8ms]
2025-06-13 09:18:49.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:54.033 [info] > git config --get commit.template [9ms]
2025-06-13 09:18:54.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:54.046 [info] > git status -z -uall [6ms]
2025-06-13 09:18:54.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:19:00.143 [info] > git config --get commit.template [1ms]
2025-06-13 09:19:00.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:19:00.175 [info] > git status -z -uall [20ms]
2025-06-13 09:19:00.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-13 09:20:16.718 [info] > git config --get commit.template [8ms]
2025-06-13 09:20:16.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:20:16.731 [info] > git status -z -uall [6ms]
2025-06-13 09:20:16.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:20:16.867 [info] > git merge-base refs/heads/main refs/remotes/origin/main [128ms]
2025-06-13 09:20:16.876 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:20:16.941 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:20:16.951 [info] > git cat-file -s 87ec41b383c9b9a3d63def2c3f40cafd19a4f5fa [2ms]
2025-06-13 09:20:17.113 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-06-13 09:20:17.113 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:20:17.181 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:20:21.749 [info] > git config --get commit.template [4ms]
2025-06-13 09:20:21.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:20:21.757 [info] > git status -z -uall [3ms]
2025-06-13 09:20:21.759 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:20:26.780 [info] > git config --get commit.template [8ms]
2025-06-13 09:20:26.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-13 09:20:26.803 [info] > git status -z -uall [9ms]
2025-06-13 09:20:26.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 09:25:59.468 [info] > git config --get commit.template [5ms]
2025-06-13 09:25:59.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:25:59.485 [info] > git status -z -uall [9ms]
2025-06-13 09:25:59.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:04.499 [info] > git config --get commit.template [5ms]
2025-06-13 09:26:04.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:04.517 [info] > git status -z -uall [9ms]
2025-06-13 09:26:04.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:26:09.539 [info] > git config --get commit.template [9ms]
2025-06-13 09:26:09.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:09.562 [info] > git status -z -uall [12ms]
2025-06-13 09:26:09.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:14.574 [info] > git config --get commit.template [1ms]
2025-06-13 09:26:14.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:14.591 [info] > git status -z -uall [7ms]
2025-06-13 09:26:14.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:19.608 [info] > git config --get commit.template [6ms]
2025-06-13 09:26:19.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:26:19.624 [info] > git status -z -uall [7ms]
2025-06-13 09:26:19.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:26:24.640 [info] > git config --get commit.template [5ms]
2025-06-13 09:26:24.642 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:24.650 [info] > git status -z -uall [4ms]
2025-06-13 09:26:24.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:42.931 [info] > git config --get commit.template [2ms]
2025-06-13 09:26:42.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:26:42.952 [info] > git status -z -uall [8ms]
2025-06-13 09:26:42.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:47.965 [info] > git config --get commit.template [2ms]
2025-06-13 09:26:47.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:47.989 [info] > git status -z -uall [8ms]
2025-06-13 09:26:47.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:26:55.561 [info] > git config --get commit.template [4ms]
2025-06-13 09:26:55.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:26:55.578 [info] > git status -z -uall [8ms]
2025-06-13 09:26:55.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:00.594 [info] > git config --get commit.template [7ms]
2025-06-13 09:27:00.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:00.611 [info] > git status -z -uall [6ms]
2025-06-13 09:27:00.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:05.633 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:05.634 [info] > git config --get commit.template [12ms]
2025-06-13 09:27:05.648 [info] > git status -z -uall [5ms]
2025-06-13 09:27:05.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:10.671 [info] > git config --get commit.template [9ms]
2025-06-13 09:27:10.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:10.691 [info] > git status -z -uall [11ms]
2025-06-13 09:27:10.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:15.705 [info] > git config --get commit.template [0ms]
2025-06-13 09:27:15.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:15.736 [info] > git status -z -uall [8ms]
2025-06-13 09:27:15.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:39:31.792 [info] > git config --get commit.template [6ms]
2025-06-13 09:39:31.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:39:31.805 [info] > git status -z -uall [5ms]
2025-06-13 09:39:31.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:39:31.940 [info] > git merge-base refs/heads/main refs/remotes/origin/main [126ms]
2025-06-13 09:39:31.950 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:39:32.091 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:39:32.100 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [1ms]
2025-06-13 09:39:32.224 [info] > git config --get --local branch.main.github-pr-owner-number [1ms]
2025-06-13 09:39:32.224 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:39:32.355 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:39:36.827 [info] > git config --get commit.template [4ms]
2025-06-13 09:39:36.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:39:36.842 [info] > git status -z -uall [8ms]
2025-06-13 09:39:36.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:39:41.853 [info] > git config --get commit.template [2ms]
2025-06-13 09:39:41.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:39:41.872 [info] > git status -z -uall [9ms]
2025-06-13 09:39:41.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:39:46.891 [info] > git config --get commit.template [6ms]
2025-06-13 09:39:46.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:39:46.905 [info] > git status -z -uall [6ms]
2025-06-13 09:39:46.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:40:19.159 [info] > git config --get commit.template [10ms]
2025-06-13 09:40:19.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:40:19.171 [info] > git status -z -uall [5ms]
2025-06-13 09:40:19.172 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:40:24.190 [info] > git config --get commit.template [7ms]
2025-06-13 09:40:24.192 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:40:24.206 [info] > git status -z -uall [7ms]
2025-06-13 09:40:24.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:40:29.222 [info] > git config --get commit.template [1ms]
2025-06-13 09:40:29.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:40:29.250 [info] > git status -z -uall [9ms]
2025-06-13 09:40:29.252 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:41:17.978 [info] > git config --get commit.template [7ms]
2025-06-13 09:41:17.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:18.007 [info] > git status -z -uall [20ms]
2025-06-13 09:41:18.007 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-13 09:41:23.022 [info] > git config --get commit.template [6ms]
2025-06-13 09:41:23.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:41:23.036 [info] > git status -z -uall [6ms]
2025-06-13 09:41:23.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:41:28.056 [info] > git config --get commit.template [6ms]
2025-06-13 09:41:28.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:28.071 [info] > git status -z -uall [7ms]
2025-06-13 09:41:28.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:41:33.081 [info] > git config --get commit.template [1ms]
2025-06-13 09:41:33.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:33.106 [info] > git status -z -uall [8ms]
2025-06-13 09:41:33.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:41:36.549 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:41:36.554 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [1ms]
2025-06-13 09:41:36.825 [info] > git show --textconv :server/csvLogger.ts [1ms]
2025-06-13 09:41:38.123 [info] > git config --get commit.template [7ms]
2025-06-13 09:41:38.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:38.136 [info] > git status -z -uall [6ms]
2025-06-13 09:41:38.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:41:38.258 [info] > git merge-base refs/heads/main refs/remotes/origin/main [115ms]
2025-06-13 09:41:38.268 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:41:38.518 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-06-13 09:41:38.518 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:41:44.084 [info] > git config --get commit.template [3ms]
2025-06-13 09:41:44.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:44.107 [info] > git status -z -uall [7ms]
2025-06-13 09:41:44.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:41:49.129 [info] > git config --get commit.template [7ms]
2025-06-13 09:41:49.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:41:49.143 [info] > git status -z -uall [6ms]
2025-06-13 09:41:49.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:41:54.159 [info] > git config --get commit.template [6ms]
2025-06-13 09:41:54.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:54.174 [info] > git status -z -uall [8ms]
2025-06-13 09:41:54.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:42:20.000 [info] > git config --get commit.template [7ms]
2025-06-13 09:42:20.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:42:20.011 [info] > git status -z -uall [5ms]
2025-06-13 09:42:20.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:42:20.219 [info] > git ls-files --stage -- server/csvLogger.ts [1ms]
2025-06-13 09:42:20.228 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [2ms]
2025-06-13 09:42:20.475 [info] > git show --textconv :server/csvLogger.ts [3ms]
2025-06-13 09:42:50.826 [info] > git config --get commit.template [7ms]
2025-06-13 09:42:50.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:42:50.840 [info] > git status -z -uall [5ms]
2025-06-13 09:42:50.841 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:42:50.975 [info] > git merge-base refs/heads/main refs/remotes/origin/main [128ms]
2025-06-13 09:42:50.984 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:42:51.044 [info] > git ls-files --stage -- server/csvLogger.ts [3ms]
2025-06-13 09:42:51.053 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [1ms]
2025-06-13 09:42:51.282 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:42:55.871 [info] > git config --get commit.template [11ms]
2025-06-13 09:42:55.873 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:42:55.888 [info] > git status -z -uall [7ms]
2025-06-13 09:42:55.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:43:38.385 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-06-13 09:43:38.385 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:45:35.108 [info] > git config --get commit.template [5ms]
2025-06-13 09:45:35.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-13 09:45:35.144 [info] > git status -z -uall [12ms]
2025-06-13 09:45:35.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:45:35.341 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:45:35.353 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [4ms]
2025-06-13 09:45:35.612 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:45:40.228 [info] > git config --get commit.template [12ms]
2025-06-13 09:45:40.235 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-13 09:45:40.263 [info] > git status -z -uall [7ms]
2025-06-13 09:45:40.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:45:48.119 [info] > git config --get commit.template [1ms]
2025-06-13 09:45:48.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:45:48.140 [info] > git status -z -uall [7ms]
2025-06-13 09:45:48.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:45:56.922 [info] > git config --get commit.template [3ms]
2025-06-13 09:45:56.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:45:56.948 [info] > git status -z -uall [12ms]
2025-06-13 09:45:56.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:46:07.454 [info] > git config --get commit.template [7ms]
2025-06-13 09:46:07.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:46:07.469 [info] > git status -z -uall [5ms]
2025-06-13 09:46:07.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:46:12.487 [info] > git config --get commit.template [8ms]
2025-06-13 09:46:12.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:46:12.502 [info] > git status -z -uall [7ms]
2025-06-13 09:46:12.503 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:46:50.933 [info] > git config --get commit.template [4ms]
2025-06-13 09:46:50.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:46:50.951 [info] > git status -z -uall [10ms]
2025-06-13 09:46:50.952 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:46:51.151 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:46:51.163 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [3ms]
2025-06-13 09:46:51.409 [info] > git show --textconv :server/csvLogger.ts [0ms]
2025-06-13 09:46:55.966 [info] > git config --get commit.template [8ms]
2025-06-13 09:46:55.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:46:55.984 [info] > git status -z -uall [9ms]
2025-06-13 09:46:55.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:01.007 [info] > git config --get commit.template [11ms]
2025-06-13 09:47:01.009 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:47:01.029 [info] > git status -z -uall [13ms]
2025-06-13 09:47:01.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-13 09:47:06.048 [info] > git config --get commit.template [6ms]
2025-06-13 09:47:06.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:47:06.065 [info] > git status -z -uall [7ms]
2025-06-13 09:47:06.066 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:11.081 [info] > git config --get commit.template [0ms]
2025-06-13 09:47:11.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:47:11.116 [info] > git status -z -uall [6ms]
2025-06-13 09:47:11.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:16.138 [info] > git config --get commit.template [9ms]
2025-06-13 09:47:16.141 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:47:16.153 [info] > git status -z -uall [8ms]
2025-06-13 09:47:16.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:21.171 [info] > git config --get commit.template [9ms]
2025-06-13 09:47:21.173 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:47:21.185 [info] > git status -z -uall [5ms]
2025-06-13 09:47:21.186 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:47:26.210 [info] > git config --get commit.template [11ms]
2025-06-13 09:47:26.214 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:47:26.231 [info] > git status -z -uall [7ms]
2025-06-13 09:47:26.233 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:33.152 [info] > git config --get commit.template [8ms]
2025-06-13 09:47:33.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:47:33.168 [info] > git status -z -uall [10ms]
2025-06-13 09:47:33.168 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:47:38.187 [info] > git config --get commit.template [10ms]
2025-06-13 09:47:38.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:47:38.209 [info] > git status -z -uall [9ms]
2025-06-13 09:47:38.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:47:43.231 [info] > git config --get commit.template [10ms]
2025-06-13 09:47:43.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:47:43.250 [info] > git status -z -uall [10ms]
2025-06-13 09:47:43.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:47:48.268 [info] > git config --get commit.template [8ms]
2025-06-13 09:47:48.270 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:47:48.279 [info] > git status -z -uall [4ms]
2025-06-13 09:47:48.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:53.299 [info] > git config --get commit.template [7ms]
2025-06-13 09:47:53.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:47:53.315 [info] > git status -z -uall [8ms]
2025-06-13 09:47:53.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:47:58.332 [info] > git config --get commit.template [7ms]
2025-06-13 09:47:58.333 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:47:58.349 [info] > git status -z -uall [8ms]
2025-06-13 09:47:58.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:48:03.367 [info] > git config --get commit.template [6ms]
2025-06-13 09:48:03.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:03.383 [info] > git status -z -uall [4ms]
2025-06-13 09:48:03.384 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:48:08.402 [info] > git config --get commit.template [8ms]
2025-06-13 09:48:08.404 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:08.417 [info] > git status -z -uall [6ms]
2025-06-13 09:48:08.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:48:13.433 [info] > git config --get commit.template [5ms]
2025-06-13 09:48:13.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:48:13.454 [info] > git status -z -uall [10ms]
2025-06-13 09:48:13.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:48:18.474 [info] > git config --get commit.template [6ms]
2025-06-13 09:48:18.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:18.486 [info] > git status -z -uall [6ms]
2025-06-13 09:48:18.487 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:48:23.507 [info] > git config --get commit.template [9ms]
2025-06-13 09:48:23.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:23.522 [info] > git status -z -uall [7ms]
2025-06-13 09:48:23.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:48:28.538 [info] > git config --get commit.template [5ms]
2025-06-13 09:48:28.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:28.549 [info] > git status -z -uall [4ms]
2025-06-13 09:48:28.550 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:48:33.573 [info] > git config --get commit.template [9ms]
2025-06-13 09:48:33.574 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:33.586 [info] > git status -z -uall [6ms]
2025-06-13 09:48:33.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:48:38.606 [info] > git config --get commit.template [8ms]
2025-06-13 09:48:38.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:38.622 [info] > git status -z -uall [7ms]
2025-06-13 09:48:38.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:48:43.640 [info] > git config --get commit.template [7ms]
2025-06-13 09:48:43.642 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:43.656 [info] > git status -z -uall [9ms]
2025-06-13 09:48:43.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:48:48.677 [info] > git config --get commit.template [8ms]
2025-06-13 09:48:48.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:48.692 [info] > git status -z -uall [7ms]
2025-06-13 09:48:48.693 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:48:53.704 [info] > git config --get commit.template [3ms]
2025-06-13 09:48:53.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:48:53.713 [info] > git status -z -uall [4ms]
2025-06-13 09:48:53.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:43:45.508 [info] > git config --get commit.template [4ms]
2025-06-13 12:43:45.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-13 12:43:45.524 [info] > git status -z -uall [4ms]
2025-06-13 12:43:45.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 12:43:45.679 [info] > git merge-base refs/heads/main refs/remotes/origin/main [131ms]
2025-06-13 12:43:45.703 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [16ms]
2025-06-13 12:43:45.762 [info] > git ls-files --stage -- server/csvLogger.ts [12ms]
2025-06-13 12:43:45.788 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [8ms]
2025-06-13 12:43:45.907 [info] > git config --get --local branch.main.github-pr-owner-number [1ms]
2025-06-13 12:43:45.907 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 12:43:46.057 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 12:43:50.559 [info] > git config --get commit.template [4ms]
2025-06-13 12:43:50.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 12:43:50.569 [info] > git status -z -uall [4ms]
2025-06-13 12:43:50.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:44:01.794 [info] > git config --get commit.template [4ms]
2025-06-13 12:44:01.795 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:44:01.808 [info] > git status -z -uall [7ms]
2025-06-13 12:44:01.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 12:44:06.834 [info] > git config --get commit.template [9ms]
2025-06-13 12:44:06.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 12:44:06.852 [info] > git status -z -uall [8ms]
2025-06-13 12:44:06.854 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 12:44:11.871 [info] > git config --get commit.template [6ms]
2025-06-13 12:44:11.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 12:44:11.886 [info] > git status -z -uall [4ms]
2025-06-13 12:44:11.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:53:21.506 [info] > git config --get commit.template [8ms]
2025-06-13 12:53:21.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 12:53:21.524 [info] > git status -z -uall [8ms]
2025-06-13 12:53:21.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 12:53:26.545 [info] > git config --get commit.template [7ms]
2025-06-13 12:53:26.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 12:53:26.571 [info] > git status -z -uall [14ms]
2025-06-13 12:53:26.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 12:53:31.584 [info] > git config --get commit.template [4ms]
2025-06-13 12:53:31.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:53:31.593 [info] > git status -z -uall [3ms]
2025-06-13 12:53:31.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 12:53:37.582 [info] > git config --get commit.template [3ms]
2025-06-13 12:53:37.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:53:37.591 [info] > git status -z -uall [4ms]
2025-06-13 12:53:37.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 12:53:42.605 [info] > git config --get commit.template [4ms]
2025-06-13 12:53:42.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:53:42.614 [info] > git status -z -uall [3ms]
2025-06-13 12:53:42.616 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:53:55.089 [info] > git config --get commit.template [2ms]
2025-06-13 12:53:55.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:53:55.103 [info] > git status -z -uall [4ms]
2025-06-13 12:53:55.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:54:06.228 [info] > git config --get commit.template [5ms]
2025-06-13 12:54:06.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:06.238 [info] > git status -z -uall [6ms]
2025-06-13 12:54:06.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 12:54:16.668 [info] > git config --get commit.template [5ms]
2025-06-13 12:54:16.668 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:16.676 [info] > git status -z -uall [4ms]
2025-06-13 12:54:16.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:54:21.689 [info] > git config --get commit.template [5ms]
2025-06-13 12:54:21.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:21.701 [info] > git status -z -uall [6ms]
2025-06-13 12:54:21.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 12:54:26.714 [info] > git config --get commit.template [4ms]
2025-06-13 12:54:26.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:26.722 [info] > git status -z -uall [3ms]
2025-06-13 12:54:26.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 12:54:32.729 [info] > git config --get commit.template [2ms]
2025-06-13 12:54:32.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 12:54:32.744 [info] > git status -z -uall [5ms]
2025-06-13 12:54:32.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:54:41.409 [info] > git config --get commit.template [5ms]
2025-06-13 12:54:41.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:41.428 [info] > git status -z -uall [6ms]
2025-06-13 12:54:41.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:54:48.072 [info] > git config --get commit.template [6ms]
2025-06-13 12:54:48.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:48.082 [info] > git status -z -uall [4ms]
2025-06-13 12:54:48.083 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 12:54:53.095 [info] > git config --get commit.template [5ms]
2025-06-13 12:54:53.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:53.105 [info] > git status -z -uall [4ms]
2025-06-13 12:54:53.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 12:54:58.119 [info] > git config --get commit.template [5ms]
2025-06-13 12:54:58.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 12:54:58.127 [info] > git status -z -uall [4ms]
2025-06-13 12:54:58.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:55:03.151 [info] > git config --get commit.template [8ms]
2025-06-13 12:55:03.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 12:55:03.164 [info] > git status -z -uall [5ms]
2025-06-13 12:55:03.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:55:34.696 [info] > git config --get commit.template [1ms]
2025-06-13 12:55:34.717 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 12:55:34.730 [info] > git status -z -uall [7ms]
2025-06-13 12:55:34.730 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 12:55:34.743 [info] > git merge-base refs/heads/main refs/remotes/origin/main [2ms]
2025-06-13 12:55:34.751 [info] > git diff --name-status -z --diff-filter=ADMR b548afc48e9ad9ba098021624326a42ab83aa9aa...refs/remotes/origin/main [3ms]
2025-06-13 12:55:34.891 [info] > git ls-files --stage -- server/csvLogger.ts [1ms]
2025-06-13 12:55:34.896 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [1ms]
2025-06-13 12:55:34.973 [info] > git config --get --local branch.main.github-pr-owner-number [3ms]
2025-06-13 12:55:34.973 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 12:55:35.093 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 12:55:39.753 [info] > git config --get commit.template [4ms]
2025-06-13 12:55:39.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 12:55:39.763 [info] > git status -z -uall [5ms]
2025-06-13 12:55:39.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
