2025-06-13 09:07:31.427 [info] [main] Log level: Info
2025-06-13 09:07:31.427 [info] [main] Validating found git in: "git"
2025-06-13 09:07:31.427 [info] [main] Using git "2.47.2" from "git"
2025-06-13 09:07:31.427 [info] [Model][doInitialScan] Initial repository scan started
2025-06-13 09:07:31.427 [info] > git rev-parse --show-toplevel [508ms]
2025-06-13 09:07:31.427 [info] > git rev-parse --git-dir --git-common-dir [2146ms]
2025-06-13 09:07:31.427 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-13 09:07:31.427 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-13 09:07:31.427 [info] > git rev-parse --show-toplevel [97ms]
2025-06-13 09:07:31.427 [info] > git config --get commit.template [99ms]
2025-06-13 09:07:31.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [520ms]
2025-06-13 09:07:31.948 [info] > git rev-parse --show-toplevel [4ms]
2025-06-13 09:07:31.949 [info] > git fetch [659ms]
2025-06-13 09:07:31.949 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/BeamTechLandingPage/'
2025-06-13 09:07:32.072 [info] > git config --get commit.template [117ms]
2025-06-13 09:07:32.072 [info] > git rev-parse --show-toplevel [120ms]
2025-06-13 09:07:32.118 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [38ms]
2025-06-13 09:07:32.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-13 09:07:32.133 [info] > git config --get commit.template [31ms]
2025-06-13 09:07:32.133 [info] > git rev-parse --show-toplevel [37ms]
2025-06-13 09:07:33.149 [info] > git config --get --local branch.main.vscode-merge-base [1024ms]
2025-06-13 09:07:33.388 [info] > git check-ignore -v -z --stdin [207ms]
2025-06-13 09:07:33.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [230ms]
2025-06-13 09:07:33.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [210ms]
2025-06-13 09:07:33.409 [info] > git rev-parse --show-toplevel [218ms]
2025-06-13 09:07:33.417 [info] > git merge-base refs/heads/main refs/remotes/origin/main [21ms]
2025-06-13 09:07:33.441 [info] > git rev-parse --show-toplevel [25ms]
2025-06-13 09:07:33.452 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [30ms]
2025-06-13 09:07:33.762 [info] > git status -z -uall [11ms]
2025-06-13 09:07:33.763 [info] > git merge-base refs/heads/main refs/remotes/origin/main [335ms]
2025-06-13 09:07:34.191 [info] > git diff --name-status -z --diff-filter=ADMR 7e20b43410617e622db822be962b23388a2a658c...refs/remotes/origin/main [420ms]
2025-06-13 09:07:34.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [432ms]
2025-06-13 09:07:34.199 [info] > git rev-parse --show-toplevel [750ms]
2025-06-13 09:07:34.726 [info] > git rev-parse --show-toplevel [404ms]
2025-06-13 09:07:35.221 [info] > git rev-parse --show-toplevel [49ms]
2025-06-13 09:07:35.223 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-13 09:07:35.303 [info] > git show --textconv :server/csvLogger.ts [64ms]
2025-06-13 09:07:35.333 [info] > git ls-files --stage -- server/csvLogger.ts [86ms]
2025-06-13 09:07:35.343 [info] > git cat-file -s 563bb11ecd72c4f876000ef8becf4c1b5e588986 [3ms]
2025-06-13 09:07:35.490 [info] > git config --get commit.template [12ms]
2025-06-13 09:07:35.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [96ms]
2025-06-13 09:07:35.671 [info] > git status -z -uall [14ms]
2025-06-13 09:07:35.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [180ms]
2025-06-13 09:07:37.106 [info] > git config --get --local branch.main.github-pr-owner-number [154ms]
2025-06-13 09:07:37.106 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:09:18.132 [info] > git config --get commit.template [9ms]
2025-06-13 09:09:18.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:18.142 [info] > git status -z -uall [4ms]
2025-06-13 09:09:18.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:09:18.351 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:09:18.361 [info] > git cat-file -s 563bb11ecd72c4f876000ef8becf4c1b5e588986 [2ms]
2025-06-13 09:09:18.556 [info] > git show --textconv :server/csvLogger.ts [1ms]
2025-06-13 09:09:23.188 [info] > git config --get commit.template [11ms]
2025-06-13 09:09:23.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:09:23.251 [info] > git status -z -uall [37ms]
2025-06-13 09:09:23.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [35ms]
2025-06-13 09:09:28.299 [info] > git config --get commit.template [8ms]
2025-06-13 09:09:28.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:28.312 [info] > git status -z -uall [5ms]
2025-06-13 09:09:28.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:09:38.583 [info] > git config --get commit.template [22ms]
2025-06-13 09:09:38.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:09:38.612 [info] > git status -z -uall [8ms]
2025-06-13 09:09:38.616 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:09:43.630 [info] > git config --get commit.template [4ms]
2025-06-13 09:09:43.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:43.639 [info] > git status -z -uall [3ms]
2025-06-13 09:09:43.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:09:48.659 [info] > git config --get commit.template [7ms]
2025-06-13 09:09:48.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:48.671 [info] > git status -z -uall [4ms]
2025-06-13 09:09:48.674 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:09:53.699 [info] > git config --get commit.template [13ms]
2025-06-13 09:09:53.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:09:53.715 [info] > git status -z -uall [9ms]
2025-06-13 09:09:53.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 09:09:58.737 [info] > git config --get commit.template [8ms]
2025-06-13 09:09:58.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:09:58.751 [info] > git status -z -uall [7ms]
2025-06-13 09:09:58.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:10:03.773 [info] > git config --get commit.template [12ms]
2025-06-13 09:10:03.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:10:03.790 [info] > git status -z -uall [7ms]
2025-06-13 09:10:03.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:10:08.804 [info] > git config --get commit.template [4ms]
2025-06-13 09:10:08.805 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:10:08.819 [info] > git status -z -uall [6ms]
2025-06-13 09:10:08.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:16:17.469 [info] > git config --get commit.template [16ms]
2025-06-13 09:16:17.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:16:17.486 [info] > git status -z -uall [7ms]
2025-06-13 09:16:17.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:16:22.505 [info] > git config --get commit.template [6ms]
2025-06-13 09:16:22.507 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:16:22.520 [info] > git status -z -uall [6ms]
2025-06-13 09:16:22.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:16:27.535 [info] > git config --get commit.template [0ms]
2025-06-13 09:16:27.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:16:27.557 [info] > git status -z -uall [9ms]
2025-06-13 09:16:27.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:16:32.571 [info] > git config --get commit.template [6ms]
2025-06-13 09:16:32.573 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:16:32.586 [info] > git status -z -uall [6ms]
2025-06-13 09:16:32.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:17:25.786 [info] > git config --get commit.template [8ms]
2025-06-13 09:17:25.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:17:25.799 [info] > git status -z -uall [6ms]
2025-06-13 09:17:25.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:17:57.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:17:57.430 [info] > git config --get commit.template [9ms]
2025-06-13 09:17:57.443 [info] > git status -z -uall [7ms]
2025-06-13 09:17:57.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:17:57.579 [info] > git merge-base refs/heads/main refs/remotes/origin/main [126ms]
2025-06-13 09:17:57.588 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:17:57.654 [info] > git ls-files --stage -- server/csvLogger.ts [1ms]
2025-06-13 09:17:57.659 [info] > git cat-file -s 87ec41b383c9b9a3d63def2c3f40cafd19a4f5fa [2ms]
2025-06-13 09:17:57.814 [info] > git config --get --local branch.main.github-pr-owner-number [3ms]
2025-06-13 09:17:57.814 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:17:57.895 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:18:02.474 [info] > git config --get commit.template [3ms]
2025-06-13 09:18:02.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:18:02.497 [info] > git status -z -uall [6ms]
2025-06-13 09:18:02.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:23.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-13 09:18:23.817 [info] > git config --get commit.template [17ms]
2025-06-13 09:18:23.846 [info] > git status -z -uall [12ms]
2025-06-13 09:18:23.847 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:18:28.857 [info] > git config --get commit.template [2ms]
2025-06-13 09:18:28.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:28.877 [info] > git status -z -uall [7ms]
2025-06-13 09:18:28.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:33.890 [info] > git config --get commit.template [1ms]
2025-06-13 09:18:33.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:33.910 [info] > git status -z -uall [5ms]
2025-06-13 09:18:33.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:38.924 [info] > git config --get commit.template [2ms]
2025-06-13 09:18:38.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:38.953 [info] > git status -z -uall [10ms]
2025-06-13 09:18:38.955 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:18:43.971 [info] > git config --get commit.template [6ms]
2025-06-13 09:18:43.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:43.984 [info] > git status -z -uall [7ms]
2025-06-13 09:18:43.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:18:48.998 [info] > git config --get commit.template [4ms]
2025-06-13 09:18:48.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:18:49.016 [info] > git status -z -uall [8ms]
2025-06-13 09:18:49.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:18:54.033 [info] > git config --get commit.template [9ms]
2025-06-13 09:18:54.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:18:54.046 [info] > git status -z -uall [6ms]
2025-06-13 09:18:54.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:19:00.143 [info] > git config --get commit.template [1ms]
2025-06-13 09:19:00.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:19:00.175 [info] > git status -z -uall [20ms]
2025-06-13 09:19:00.180 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-13 09:20:16.718 [info] > git config --get commit.template [8ms]
2025-06-13 09:20:16.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:20:16.731 [info] > git status -z -uall [6ms]
2025-06-13 09:20:16.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:20:16.867 [info] > git merge-base refs/heads/main refs/remotes/origin/main [128ms]
2025-06-13 09:20:16.876 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:20:16.941 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:20:16.951 [info] > git cat-file -s 87ec41b383c9b9a3d63def2c3f40cafd19a4f5fa [2ms]
2025-06-13 09:20:17.113 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-06-13 09:20:17.113 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:20:17.181 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:20:21.749 [info] > git config --get commit.template [4ms]
2025-06-13 09:20:21.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:20:21.757 [info] > git status -z -uall [3ms]
2025-06-13 09:20:21.759 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:20:26.780 [info] > git config --get commit.template [8ms]
2025-06-13 09:20:26.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-13 09:20:26.803 [info] > git status -z -uall [9ms]
2025-06-13 09:20:26.804 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-13 09:25:59.468 [info] > git config --get commit.template [5ms]
2025-06-13 09:25:59.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:25:59.485 [info] > git status -z -uall [9ms]
2025-06-13 09:25:59.486 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:04.499 [info] > git config --get commit.template [5ms]
2025-06-13 09:26:04.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:04.517 [info] > git status -z -uall [9ms]
2025-06-13 09:26:04.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:26:09.539 [info] > git config --get commit.template [9ms]
2025-06-13 09:26:09.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:09.562 [info] > git status -z -uall [12ms]
2025-06-13 09:26:09.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:14.574 [info] > git config --get commit.template [1ms]
2025-06-13 09:26:14.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:14.591 [info] > git status -z -uall [7ms]
2025-06-13 09:26:14.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:19.608 [info] > git config --get commit.template [6ms]
2025-06-13 09:26:19.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:26:19.624 [info] > git status -z -uall [7ms]
2025-06-13 09:26:19.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:26:24.640 [info] > git config --get commit.template [5ms]
2025-06-13 09:26:24.642 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:24.650 [info] > git status -z -uall [4ms]
2025-06-13 09:26:24.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:42.931 [info] > git config --get commit.template [2ms]
2025-06-13 09:26:42.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:26:42.952 [info] > git status -z -uall [8ms]
2025-06-13 09:26:42.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:26:47.965 [info] > git config --get commit.template [2ms]
2025-06-13 09:26:47.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:26:47.989 [info] > git status -z -uall [8ms]
2025-06-13 09:26:47.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:26:55.561 [info] > git config --get commit.template [4ms]
2025-06-13 09:26:55.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-13 09:26:55.578 [info] > git status -z -uall [8ms]
2025-06-13 09:26:55.578 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:00.594 [info] > git config --get commit.template [7ms]
2025-06-13 09:27:00.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:00.611 [info] > git status -z -uall [6ms]
2025-06-13 09:27:00.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:05.633 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:05.634 [info] > git config --get commit.template [12ms]
2025-06-13 09:27:05.648 [info] > git status -z -uall [5ms]
2025-06-13 09:27:05.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:10.671 [info] > git config --get commit.template [9ms]
2025-06-13 09:27:10.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:10.691 [info] > git status -z -uall [11ms]
2025-06-13 09:27:10.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:27:15.705 [info] > git config --get commit.template [0ms]
2025-06-13 09:27:15.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:27:15.736 [info] > git status -z -uall [8ms]
2025-06-13 09:27:15.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:39:31.792 [info] > git config --get commit.template [6ms]
2025-06-13 09:39:31.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:39:31.805 [info] > git status -z -uall [5ms]
2025-06-13 09:39:31.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:39:31.940 [info] > git merge-base refs/heads/main refs/remotes/origin/main [126ms]
2025-06-13 09:39:31.950 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:39:32.091 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:39:32.100 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [1ms]
2025-06-13 09:39:32.224 [info] > git config --get --local branch.main.github-pr-owner-number [1ms]
2025-06-13 09:39:32.224 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:39:32.355 [info] > git show --textconv :server/csvLogger.ts [2ms]
2025-06-13 09:39:36.827 [info] > git config --get commit.template [4ms]
2025-06-13 09:39:36.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:39:36.842 [info] > git status -z -uall [8ms]
2025-06-13 09:39:36.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:39:41.853 [info] > git config --get commit.template [2ms]
2025-06-13 09:39:41.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:39:41.872 [info] > git status -z -uall [9ms]
2025-06-13 09:39:41.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-13 09:39:46.891 [info] > git config --get commit.template [6ms]
2025-06-13 09:39:46.893 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:39:46.905 [info] > git status -z -uall [6ms]
2025-06-13 09:39:46.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:40:19.159 [info] > git config --get commit.template [10ms]
2025-06-13 09:40:19.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:40:19.171 [info] > git status -z -uall [5ms]
2025-06-13 09:40:19.172 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:40:24.190 [info] > git config --get commit.template [7ms]
2025-06-13 09:40:24.192 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:40:24.206 [info] > git status -z -uall [7ms]
2025-06-13 09:40:24.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:40:29.222 [info] > git config --get commit.template [1ms]
2025-06-13 09:40:29.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:40:29.250 [info] > git status -z -uall [9ms]
2025-06-13 09:40:29.252 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:41:17.978 [info] > git config --get commit.template [7ms]
2025-06-13 09:41:17.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:18.007 [info] > git status -z -uall [20ms]
2025-06-13 09:41:18.007 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-13 09:41:23.022 [info] > git config --get commit.template [6ms]
2025-06-13 09:41:23.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:41:23.036 [info] > git status -z -uall [6ms]
2025-06-13 09:41:23.038 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:41:28.056 [info] > git config --get commit.template [6ms]
2025-06-13 09:41:28.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:28.071 [info] > git status -z -uall [7ms]
2025-06-13 09:41:28.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:41:33.081 [info] > git config --get commit.template [1ms]
2025-06-13 09:41:33.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:33.106 [info] > git status -z -uall [8ms]
2025-06-13 09:41:33.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:41:36.549 [info] > git ls-files --stage -- server/csvLogger.ts [2ms]
2025-06-13 09:41:36.554 [info] > git cat-file -s 1fce6f51d7d389e752a39c0b2b781e52270f5632 [1ms]
2025-06-13 09:41:36.825 [info] > git show --textconv :server/csvLogger.ts [1ms]
2025-06-13 09:41:38.123 [info] > git config --get commit.template [7ms]
2025-06-13 09:41:38.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:38.136 [info] > git status -z -uall [6ms]
2025-06-13 09:41:38.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-13 09:41:38.258 [info] > git merge-base refs/heads/main refs/remotes/origin/main [115ms]
2025-06-13 09:41:38.268 [info] > git diff --name-status -z --diff-filter=ADMR e7f2cf0fcf9ba52132e8db0854ef6d4affa6371c...refs/remotes/origin/main [2ms]
2025-06-13 09:41:38.518 [info] > git config --get --local branch.main.github-pr-owner-number [2ms]
2025-06-13 09:41:38.518 [warning] [Git][config] git config failed: Failed to execute git
2025-06-13 09:41:44.084 [info] > git config --get commit.template [3ms]
2025-06-13 09:41:44.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:44.107 [info] > git status -z -uall [7ms]
2025-06-13 09:41:44.108 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-13 09:41:49.129 [info] > git config --get commit.template [7ms]
2025-06-13 09:41:49.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-13 09:41:49.143 [info] > git status -z -uall [6ms]
2025-06-13 09:41:49.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-13 09:41:54.159 [info] > git config --get commit.template [6ms]
2025-06-13 09:41:54.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-13 09:41:54.174 [info] > git status -z -uall [8ms]
2025-06-13 09:41:54.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
