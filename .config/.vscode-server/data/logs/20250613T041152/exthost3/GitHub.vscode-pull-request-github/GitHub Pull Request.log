2025-06-13 09:07:35.342 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-13 09:07:35.342 [info] [Activation] Extension version: 0.111.**********
2025-06-13 09:07:35.871 [info] [Authentication] Creating hub for .com
2025-06-13 09:07:36.473 [info] [Activation] Looking for git repository
2025-06-13 09:07:36.473 [info] [Activation] Found 0 repositories during activation
2025-06-13 09:07:36.473 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-13 09:07:36.477 [info] [GitAPI] Registering git provider
2025-06-13 09:07:36.478 [info] [Review+0] Validate state in progress
2025-06-13 09:07:36.478 [info] [Review+0] Validating state...
2025-06-13 09:07:36.679 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:07:36.941 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:07:36.941 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:07:36.943 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-13 09:07:36.952 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:07:36.958 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:07:36.961 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:07:37.106 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:07:37.488 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-13 09:07:37.520 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:07:37.521 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:07:37.794 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:09:18.145 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-13 09:09:18.145 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-13 09:09:36.478 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 09:17:57.446 [info] [Review+0] Queuing additional validate state
2025-06-13 09:17:57.446 [info] [Review+0] Validating state...
2025-06-13 09:17:57.453 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:17:57.457 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:17:57.457 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:17:57.761 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:17:57.761 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:17:57.803 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:17:57.804 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:17:57.804 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 09:17:57.815 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:19:57.448 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 09:20:16.733 [info] [Review+0] Queuing additional validate state
2025-06-13 09:20:16.733 [info] [Review+0] Validating state...
2025-06-13 09:20:16.740 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:20:16.740 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:20:16.742 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:20:17.104 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:20:17.104 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:20:17.105 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 09:20:17.113 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:20:17.113 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:20:17.113 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:22:16.733 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 09:39:31.807 [info] [Review+0] Queuing additional validate state
2025-06-13 09:39:31.807 [info] [Review+0] Validating state...
2025-06-13 09:39:31.816 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:39:31.817 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:39:31.818 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:39:32.151 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:39:32.151 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:39:32.214 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:39:32.215 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:39:32.215 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 09:39:32.225 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:41:31.809 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 09:41:38.137 [info] [Review+0] Queuing additional validate state
2025-06-13 09:41:38.138 [info] [Review+0] Validating state...
2025-06-13 09:41:38.144 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:41:38.144 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:41:38.145 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:41:38.466 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:41:38.466 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:41:38.508 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:41:38.508 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:41:38.508 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 09:41:38.518 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:42:50.842 [info] [Review+0] Queuing additional validate state
2025-06-13 09:42:50.848 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:42:50.848 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:42:50.849 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:42:51.216 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 09:42:51.216 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 09:43:38.138 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 09:43:38.138 [info] [Review+0] Validating state...
2025-06-13 09:43:38.370 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 09:43:38.371 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 09:43:38.371 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 09:43:38.385 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 09:45:38.139 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 12:43:45.534 [info] [Review+0] Queuing additional validate state
2025-06-13 12:43:45.540 [info] [Review+0] Validating state...
2025-06-13 12:43:45.549 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 12:43:45.550 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 12:43:45.550 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 12:43:45.893 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 12:43:45.895 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 12:43:45.895 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 12:43:45.908 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-13 12:43:45.949 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.. 
2025-06-13 12:43:45.950 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/BeamTechLandingPage'.
2025-06-13 12:45:45.543 [error] [Review+0] Timeout occurred while validating state.
2025-06-13 12:55:34.733 [info] [Review+0] Queuing additional validate state
2025-06-13 12:55:34.733 [info] [Review+0] Validating state...
2025-06-13 12:55:34.963 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-13 12:55:34.963 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-13 12:55:34.964 [info] [FolderRepositoryManager+0] Using in-memory cached assignable users.
2025-06-13 12:55:34.973 [info] [Review+0] No matching pull request metadata found locally for current branch main
