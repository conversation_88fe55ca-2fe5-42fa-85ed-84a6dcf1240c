2025-06-13 09:07:26.123 [info] Extension host with pid 27916 started
2025-06-13 09:07:26.124 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/vscode.lock': Lock acquired.
2025-06-13 09:07:26.474 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-13 09:07:26.475 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-13 09:07:26.475 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescript'
2025-06-13 09:07:27.587 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-13 09:07:27.588 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-13 09:07:27.650 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-13 09:07:27.842 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-13 09:07:28.114 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-06-13 09:07:28.624 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onTerminalQuickFixRequest:copilot-chat.terminalToDebuggingSuccess'
2025-06-13 09:07:29.182 [info] Eager extensions activated
2025-06-13 09:07:29.183 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 09:07:29.183 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 09:07:29.184 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 09:07:33.226 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-13 09:07:36.485 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-13 09:07:36.485 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-13 09:07:36.485 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-13 09:07:38.107 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 09:07:39.110 [error] TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'TLSSocket'
    |     property 'parser' -> object with constructor 'HTTPParser'
    --- property 'socket' closes the circle
    at JSON.stringify (<anonymous>)
    at Yk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159275)
    at Function.serializeRequestArguments (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:167257)
    at $5.U (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163497)
    at Proxy.r.<computed>.n.charCodeAt.r.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160839)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:126:159
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:7:96
    at Array.forEach (<anonymous>)
    at q2.c (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:7:84)
    at q2.onUnexpectedError (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:7:259)
    at lt (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:7:546)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:4448
2025-06-13 09:07:40.231 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at Pze.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
2025-06-13 09:09:22.121 [info] ExtensionService#_doActivateExtension vscode.markdown-language-features, startup: false, activationEvent: 'onLanguage:markdown'
2025-06-13 09:16:53.056 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:16:53.062 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:16:54.361 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:16:54.363 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:17:58.815 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 09:20:18.114 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 09:28:11.037 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:11.043 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:12.214 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:12.217 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:24.323 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:24.325 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:25.509 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:25.511 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:42.716 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:42.718 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:43.909 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:43.911 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:57.358 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:57.360 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:58.520 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:28:58.523 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:42.178 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:42.180 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:43.380 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:43.383 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:58.627 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:58.630 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:59.807 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:29:59.809 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:31:54.616 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:31:54.622 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:31:55.911 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:31:55.913 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:19.235 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:19.240 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:20.426 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:20.428 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:42.370 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:42.375 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:43.533 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:32:43.535 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:33:06.697 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:33:06.701 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:33:07.945 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:33:07.948 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:35:12.649 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:35:12.653 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:35:13.811 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:35:13.812 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:36:31.742 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:36:31.746 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:36:32.959 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:36:32.960 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:39:33.225 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 09:41:39.519 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 09:42:38.903 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:43:39.385 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 09:45:41.389 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:41.391 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:42.683 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:42.685 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:57.609 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:57.612 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:58.937 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:45:58.938 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:11.400 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:11.403 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:12.776 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:12.778 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:26.240 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:26.243 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:28.738 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:47:28.740 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:49:59.378 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:106094)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:49:59.383 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:50:00.626 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 09:50:00.628 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:171:95298)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
    at pq.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105625)
    at pq.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:147:105427)
    at $5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163308)
    at $5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163088)
    at $5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162177)
    at $5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161282)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160079)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:361:3443)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at po.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
    at lv.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
    at ud.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
    at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
    at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
    at lk.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
    at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
    at Socket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Socket.Readable.push (node:internal/streams/readable:392:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
2025-06-13 12:43:46.909 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
2025-06-13 12:55:35.982 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2955:23152
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at C1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.111.2025060504/dist/extension.js:2907:21162)
