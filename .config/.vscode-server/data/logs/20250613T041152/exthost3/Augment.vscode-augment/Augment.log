2025-06-13 09:07:31.947 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 09:07:31.947 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"I am using VScode on a macbook. Everytime we make changes please make sure my augment-guidelines.md gets updated to reflect the correct way to do things I want to make sure we always maintain a consistent set of standards that we use for everything we do. Please ensure you search the standards before updating so that we can ensure there is no duplication and a clean layout of rules and standards is always maintained."},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 09:07:31.947 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":""}
2025-06-13 09:07:31.947 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2027 msec late.
2025-06-13 09:07:31.994 [info] 'AugmentExtension' Retrieving model config
2025-06-13 09:07:33.781 [info] 'AugmentExtension' Retrieved model config
2025-06-13 09:07:33.782 [info] 'AugmentExtension' Returning model config
2025-06-13 09:07:33.836 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.449.0"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-13 09:07:33.836 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/13/2025, 1:20:03 AM
2025-06-13 09:07:33.836 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-13 09:07:33.836 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 09:07:33.836 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/13/2025, 1:20:03 AM; type = explicit
2025-06-13 09:07:33.836 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-13 09:07:33.836 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/13/2025, 1:20:03 AM
2025-06-13 09:07:33.906 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 09:07:33.907 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 09:07:33.907 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 09:07:33.918 [info] 'ToolsModel' Loaded saved chat mode: CHAT
2025-06-13 09:07:33.921 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:07:34.684 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 09:07:34.684 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 09:07:34.684 [info] 'TaskManager' Setting current root task UUID to 0da89532-9c4b-4614-b2f1-06a488c36390
2025-06-13 09:07:35.487 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-13 09:07:35.499 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-13 09:07:35.499 [info] 'OpenFileManager' Opened source folder 100
2025-06-13 09:07:35.500 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-13 09:07:35.504 [info] 'MtimeCache[workspace]' read 1521 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-13 09:07:36.028 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/temp
2025-06-13 09:07:36.178 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:07:36.594 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250613T041152/exthost3/output_logging_20250613T090726
2025-06-13 09:07:37.430 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.json-language-features
2025-06-13 09:08:26.716 [error] 'AugmentExtension' API request 5c91afcc-8f99-435e-9f82-a4ad156c5efb to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 09:08:26.930 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:08:27.291 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 09:09:20.234 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-13 09:09:20.234 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 09:09:20.234 [info] 'TaskManager' Setting current root task UUID to 4fa79aeb-a20a-45c7-97df-01eb941a5c99
2025-06-13 09:09:20.234 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 09:09:20.234 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 09:09:21.100 [info] 'ToolsModel' Host: remoteToolHost (2 tools: 27 enabled, 0 disabled})
 + web-search
 + github-api

2025-06-13 09:09:21.100 [info] 'ToolsModel' Host: sidecarToolHost (9 tools: 149 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-13 09:09:21.381 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document--1749788817585-74e4f5ce-35be-4f47-a647-d758470a1ba3.json
2025-06-13 09:09:29.705 [error] 'AugmentExtension' API request 398c81e5-c442-42f1-8cea-4f48890e2996 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 09:09:29.958 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:09:30.357 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 09:09:54.357 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:09:54.358 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6432 bytes)
2025-06-13 09:10:02.238 [info] 'ViewTool' Tool called with path: server/csvLogger.ts and view_range: [150,-1]
2025-06-13 09:10:09.961 [info] 'ViewTool' Tool called with path: server/csvLogger.ts and view_range: [1,50]
2025-06-13 09:10:21.343 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:21.343 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6432 bytes)
2025-06-13 09:10:23.653 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:23.653 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6470 bytes)
2025-06-13 09:10:35.699 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:35.699 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6470 bytes)
2025-06-13 09:10:38.263 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:38.263 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6494 bytes)
2025-06-13 09:14:02.224 [info] 'ViewTool' Tool called with path: server/logs/contact-form-submissions.txt and view_range: undefined
2025-06-13 09:16:28.631 [info] 'ViewTool' Tool called with path: server/logs/newsletter-subscriptions.txt and view_range: undefined
2025-06-13 09:16:51.641 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:16:52.328 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12377 bytes)
2025-06-13 09:16:55.366 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:16:55.366 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12489 bytes)
2025-06-13 09:17:58.752 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-13 09:17:58.753 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:17:58.753 [info] 'TaskManager' Setting current root task UUID to 0da89532-9c4b-4614-b2f1-06a488c36390
2025-06-13 09:17:58.753 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 09:17:58.922 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-13 09:17:58.922 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 09:17:58.922 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 09:17:59.728 [info] 'ToolsModel' Host: remoteToolHost (2 tools: 27 enabled, 0 disabled})
 + web-search
 + github-api

2025-06-13 09:17:59.728 [info] 'ToolsModel' Host: sidecarToolHost (9 tools: 149 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-13 09:19:41.851 [error] 'AugmentExtensionSidecar' API request 708f9a97-31e3-44be-ac3a-57f8d424c911 to https://i0.api.augmentcode.com/chat-stream failed: The operation was aborted due to timeout
2025-06-13 09:19:41.851 [error] 'AugmentExtensionSidecar' TimeoutError: The operation was aborted due to timeout
    at node:internal/deps/undici/undici:13510:13
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VQ.OJ.globalThis.fetch (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
    at Xv (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:13135)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:886:4048)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:58285)
    at VQ.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:18579)
    at e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:35635)
    at e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:33802)
    at mD.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:2168:3225)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:907:5048
2025-06-13 09:19:42.113 [error] 'ChatApp' Chat stream failed: Error: The operation was aborted due to timeout
Error: The operation was aborted due to timeout
    at Function.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:9926)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:886:4281)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:58285)
    at VQ.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:18579)
    at e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:35635)
    at e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:33802)
    at mD.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:2168:3225)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:907:5048
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 401
  - files emitted: 1659
  - other paths emitted: 5
  - total paths emitted: 2065
  - timing stats:
    - readDir: 11 ms
    - filter: 110 ms
    - yield: 18 ms
    - total: 146 ms
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2802
  - paths not accessible: 0
  - not plain files: 0
  - large files: 29
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1513
  - mtime cache misses: 1289
  - probe batches: 291
  - blob names probed: 2920
  - files read: 1515
  - blobs uploaded: 116
  - timing stats:
    - ingestPath: 85 ms
    - probe: 92598 ms
    - stat: 41 ms
    - read: 2893 ms
    - upload: 19283 ms
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 13 ms
  - read MtimeCache: 4 ms
  - pre-populate PathMap: 62 ms
  - create PathFilter: 121 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 151 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 869997 ms
  - enable persist: 4 ms
  - total: 870353 ms
2025-06-13 09:22:05.841 [info] 'WorkspaceManager' Workspace startup complete in 872027 ms
2025-06-13 09:25:32.810 [error] 'AugmentExtension' API request 8d975f0e-2aaa-4cd1-aa34-449156b9b546 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 09:25:33.067 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:25:33.453 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
