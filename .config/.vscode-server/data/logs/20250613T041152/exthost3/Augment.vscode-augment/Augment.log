2025-06-13 09:07:31.947 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-13 09:07:31.947 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":"I am using VScode on a macbook. Everytime we make changes please make sure my augment-guidelines.md gets updated to reflect the correct way to do things I want to make sure we always maintain a consistent set of standards that we use for everything we do. Please ensure you search the standards before updating so that we can ensure there is no duplication and a clean layout of rules and standards is always maintained."},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-13 09:07:31.947 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":""}
2025-06-13 09:07:31.947 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2027 msec late.
2025-06-13 09:07:31.994 [info] 'AugmentExtension' Retrieving model config
2025-06-13 09:07:33.781 [info] 'AugmentExtension' Retrieved model config
2025-06-13 09:07:33.782 [info] 'AugmentExtension' Returning model config
2025-06-13 09:07:33.836 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.449.0"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-13 09:07:33.836 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/13/2025, 1:20:03 AM
2025-06-13 09:07:33.836 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-13 09:07:33.836 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-13 09:07:33.836 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/13/2025, 1:20:03 AM; type = explicit
2025-06-13 09:07:33.836 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-13 09:07:33.836 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/13/2025, 1:20:03 AM
2025-06-13 09:07:33.906 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-13 09:07:33.907 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-13 09:07:33.907 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-13 09:07:33.918 [info] 'ToolsModel' Loaded saved chat mode: CHAT
2025-06-13 09:07:33.921 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:07:34.684 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-13 09:07:34.684 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-13 09:07:34.684 [info] 'TaskManager' Setting current root task UUID to 0da89532-9c4b-4614-b2f1-06a488c36390
2025-06-13 09:07:35.487 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-13 09:07:35.499 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-13 09:07:35.499 [info] 'OpenFileManager' Opened source folder 100
2025-06-13 09:07:35.500 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-13 09:07:35.504 [info] 'MtimeCache[workspace]' read 1521 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-13 09:07:36.028 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/temp
2025-06-13 09:07:36.178 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:07:36.594 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250613T041152/exthost3/output_logging_20250613T090726
2025-06-13 09:07:37.430 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250613T041152/exthost3/vscode.json-language-features
2025-06-13 09:08:26.716 [error] 'AugmentExtension' API request 5c91afcc-8f99-435e-9f82-a4ad156c5efb to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 09:08:26.930 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:08:27.291 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 09:09:20.234 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-13 09:09:20.234 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 09:09:20.234 [info] 'TaskManager' Setting current root task UUID to 4fa79aeb-a20a-45c7-97df-01eb941a5c99
2025-06-13 09:09:20.234 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 09:09:20.234 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 09:09:21.100 [info] 'ToolsModel' Host: remoteToolHost (2 tools: 27 enabled, 0 disabled})
 + web-search
 + github-api

2025-06-13 09:09:21.100 [info] 'ToolsModel' Host: sidecarToolHost (9 tools: 149 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-13 09:09:21.381 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/603085a8-feef-4b64-ac7a-573b7b604034/document--1749788817585-74e4f5ce-35be-4f47-a647-d758470a1ba3.json
2025-06-13 09:09:29.705 [error] 'AugmentExtension' API request 398c81e5-c442-42f1-8cea-4f48890e2996 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 09:09:29.958 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:09:30.357 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 09:09:54.357 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:09:54.358 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6432 bytes)
2025-06-13 09:10:02.238 [info] 'ViewTool' Tool called with path: server/csvLogger.ts and view_range: [150,-1]
2025-06-13 09:10:09.961 [info] 'ViewTool' Tool called with path: server/csvLogger.ts and view_range: [1,50]
2025-06-13 09:10:21.343 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:21.343 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6432 bytes)
2025-06-13 09:10:23.653 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:23.653 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6470 bytes)
2025-06-13 09:10:35.699 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:35.699 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6470 bytes)
2025-06-13 09:10:38.263 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:10:38.263 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6494 bytes)
2025-06-13 09:14:02.224 [info] 'ViewTool' Tool called with path: server/logs/contact-form-submissions.txt and view_range: undefined
2025-06-13 09:16:28.631 [info] 'ViewTool' Tool called with path: server/logs/newsletter-subscriptions.txt and view_range: undefined
2025-06-13 09:16:51.641 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:16:52.328 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12377 bytes)
2025-06-13 09:16:55.366 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:16:55.366 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12489 bytes)
2025-06-13 09:17:58.752 [info] 'ToolsModel' Saved chat mode: CHAT
2025-06-13 09:17:58.753 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-06-13 09:17:58.753 [info] 'TaskManager' Setting current root task UUID to 0da89532-9c4b-4614-b2f1-06a488c36390
2025-06-13 09:17:58.753 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-13 09:17:58.922 [info] 'ToolsModel' Saved chat mode: AGENT
2025-06-13 09:17:58.922 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-13 09:17:58.922 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-13 09:17:59.728 [info] 'ToolsModel' Host: remoteToolHost (2 tools: 27 enabled, 0 disabled})
 + web-search
 + github-api

2025-06-13 09:17:59.728 [info] 'ToolsModel' Host: sidecarToolHost (9 tools: 149 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-13 09:19:41.851 [error] 'AugmentExtensionSidecar' API request 708f9a97-31e3-44be-ac3a-57f8d424c911 to https://i0.api.augmentcode.com/chat-stream failed: The operation was aborted due to timeout
2025-06-13 09:19:41.851 [error] 'AugmentExtensionSidecar' TimeoutError: The operation was aborted due to timeout
    at node:internal/deps/undici/undici:13510:13
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VQ.OJ.globalThis.fetch (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
    at Xv (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:13135)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:886:4048)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:58285)
    at VQ.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:18579)
    at e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:35635)
    at e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:33802)
    at mD.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:2168:3225)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:907:5048
2025-06-13 09:19:42.113 [error] 'ChatApp' Chat stream failed: Error: The operation was aborted due to timeout
Error: The operation was aborted due to timeout
    at Function.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:9926)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:886:4281)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VQ.callApiStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:58285)
    at VQ.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:18579)
    at e.startChatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:35635)
    at e.chatStream (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:1107:33802)
    at mD.onUserSendMessage (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:2168:3225)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:907:5048
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 401
  - files emitted: 1659
  - other paths emitted: 5
  - total paths emitted: 2065
  - timing stats:
    - readDir: 11 ms
    - filter: 110 ms
    - yield: 18 ms
    - total: 146 ms
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 2802
  - paths not accessible: 0
  - not plain files: 0
  - large files: 29
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1513
  - mtime cache misses: 1289
  - probe batches: 291
  - blob names probed: 2920
  - files read: 1515
  - blobs uploaded: 116
  - timing stats:
    - ingestPath: 85 ms
    - probe: 92598 ms
    - stat: 41 ms
    - read: 2893 ms
    - upload: 19283 ms
2025-06-13 09:22:05.840 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 13 ms
  - read MtimeCache: 4 ms
  - pre-populate PathMap: 62 ms
  - create PathFilter: 121 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 151 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 869997 ms
  - enable persist: 4 ms
  - total: 870353 ms
2025-06-13 09:22:05.841 [info] 'WorkspaceManager' Workspace startup complete in 872027 ms
2025-06-13 09:25:32.810 [error] 'AugmentExtension' API request 8d975f0e-2aaa-4cd1-aa34-449156b9b546 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 09:25:33.067 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:25:33.453 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 09:27:48.308 [info] 'ViewTool' Tool called with path: augment-guidelines.md and view_range: undefined
2025-06-13 09:28:09.650 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:10.337 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5152 bytes)
2025-06-13 09:28:13.219 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:13.220 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5225 bytes)
2025-06-13 09:28:15.649 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/d6a8ae223c50c6378babdc57f4e3f4ce/Augment.vscode-augment/augment-user-assets/checkpoint-documents/88fe55ca-2fe5-42fa-85ed-84a6dcf1240c
2025-06-13 09:28:24.100 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:24.100 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5225 bytes)
2025-06-13 09:28:26.511 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:26.512 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5293 bytes)
2025-06-13 09:28:42.486 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:42.486 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5293 bytes)
2025-06-13 09:28:44.914 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:44.915 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5783 bytes)
2025-06-13 09:28:57.131 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:57.131 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5783 bytes)
2025-06-13 09:28:59.525 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:28:59.526 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5798 bytes)
2025-06-13 09:29:20.025 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:29:20.025 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5798 bytes)
2025-06-13 09:29:26.324 [info] 'ViewTool' Tool called with path: client/src/components/Services.tsx and view_range: [115,130]
2025-06-13 09:29:41.918 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:29:41.918 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (5798 bytes)
2025-06-13 09:29:44.385 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:29:44.385 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (6458 bytes)
2025-06-13 09:29:58.403 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:29:58.403 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (6458 bytes)
2025-06-13 09:30:00.810 [info] 'ToolFileUtils' Reading file: client/src/components/Services.tsx
2025-06-13 09:30:00.811 [info] 'ToolFileUtils' Successfully read file: client/src/components/Services.tsx (6608 bytes)
2025-06-13 09:31:53.095 [info] 'ToolFileUtils' Reading file: client/src/utils/validation.ts
2025-06-13 09:31:53.821 [info] 'ToolFileUtils' Successfully read file: client/src/utils/validation.ts (2001 bytes)
2025-06-13 09:31:56.262 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/31e8c00c
2025-06-13 09:31:56.915 [info] 'ToolFileUtils' Reading file: client/src/utils/validation.ts
2025-06-13 09:31:56.916 [info] 'ToolFileUtils' Successfully read file: client/src/utils/validation.ts (2164 bytes)
2025-06-13 09:32:18.992 [info] 'ToolFileUtils' Reading file: client/src/utils/validation.ts
2025-06-13 09:32:18.993 [info] 'ToolFileUtils' Successfully read file: client/src/utils/validation.ts (2164 bytes)
2025-06-13 09:32:21.431 [info] 'ToolFileUtils' Reading file: client/src/utils/validation.ts
2025-06-13 09:32:21.431 [info] 'ToolFileUtils' Successfully read file: client/src/utils/validation.ts (3827 bytes)
2025-06-13 09:32:30.718 [info] 'ViewTool' Tool called with path: client/src/utils/emailService.ts and view_range: undefined
2025-06-13 09:32:41.695 [info] 'ToolFileUtils' Reading file: client/src/utils/emailService.ts
2025-06-13 09:32:41.695 [info] 'ToolFileUtils' Successfully read file: client/src/utils/emailService.ts (2803 bytes)
2025-06-13 09:32:44.536 [info] 'ToolFileUtils' Reading file: client/src/utils/emailService.ts
2025-06-13 09:32:44.536 [info] 'ToolFileUtils' Successfully read file: client/src/utils/emailService.ts (2968 bytes)
2025-06-13 09:33:06.443 [info] 'ToolFileUtils' Reading file: client/src/utils/emailService.ts
2025-06-13 09:33:06.443 [info] 'ToolFileUtils' Successfully read file: client/src/utils/emailService.ts (2968 bytes)
2025-06-13 09:33:08.950 [info] 'ToolFileUtils' Reading file: client/src/utils/emailService.ts
2025-06-13 09:33:08.951 [info] 'ToolFileUtils' Successfully read file: client/src/utils/emailService.ts (3977 bytes)
2025-06-13 09:33:37.090 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:33:37.091 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6494 bytes)
2025-06-13 09:33:39.822 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:33:39.822 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6765 bytes)
2025-06-13 09:33:56.957 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:33:56.957 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (6765 bytes)
2025-06-13 09:33:59.401 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:33:59.402 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (7324 bytes)
2025-06-13 09:34:10.835 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:34:10.836 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (7324 bytes)
2025-06-13 09:34:13.418 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:34:13.418 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (7460 bytes)
2025-06-13 09:34:36.204 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:34:36.204 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (7460 bytes)
2025-06-13 09:34:38.898 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:34:38.898 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (8236 bytes)
2025-06-13 09:34:56.047 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:34:56.047 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (8236 bytes)
2025-06-13 09:34:58.563 [info] 'ToolFileUtils' Reading file: server/csvLogger.ts
2025-06-13 09:34:58.563 [info] 'ToolFileUtils' Successfully read file: server/csvLogger.ts (8832 bytes)
2025-06-13 09:35:11.092 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-13 09:35:11.781 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (21140 bytes)
2025-06-13 09:35:14.814 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-13 09:35:14.814 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (21192 bytes)
2025-06-13 09:35:20.508 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [450,470]
2025-06-13 09:35:34.308 [error] 'AugmentExtension' API request da3c496d-25d3-4ea8-b8e0-1ec6bcbd9400 to https://i0.api.augmentcode.com/record-request-events failed: This operation was aborted
2025-06-13 09:35:34.570 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
    at Function.transientIssue (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:9926)
    at VQ.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:13224)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at VQ.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:57456)
    at VQ.logToolUseRequestEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:890:36336)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:14519
    at Is (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:12731)
    at e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:14418)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.481.0/out/extension.js:661:13741
2025-06-13 09:35:34.570 [info] 'ToolUseRequestEventReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 09:35:34.947 [info] 'ToolUseRequestEventReporter' Operation succeeded after 1 transient failures
2025-06-13 09:36:31.483 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-13 09:36:31.484 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (21192 bytes)
2025-06-13 09:36:33.964 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-06-13 09:36:33.964 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (31603 bytes)
2025-06-13 09:37:29.149 [info] 'AugmentExtension' Retrieving model config
2025-06-13 09:37:29.436 [info] 'AugmentExtension' Retrieved model config
2025-06-13 09:37:29.436 [info] 'AugmentExtension' Returning model config
2025-06-13 09:39:40.607 [info] 'ViewTool' Tool called with path: client and view_range: undefined
2025-06-13 09:39:40.828 [info] 'ViewTool' Listing directory: client (depth: 2, showHidden: false)
2025-06-13 09:39:59.318 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:00.057 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12489 bytes)
2025-06-13 09:40:03.268 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:03.268 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12717 bytes)
2025-06-13 09:40:18.478 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:18.479 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12717 bytes)
2025-06-13 09:40:18.714 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 8b7ad22c6a50ecef6fec43a80555bf14fea8ac188c855dbee02006292186fa11: deleted
2025-06-13 09:40:21.048 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:21.049 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12816 bytes)
2025-06-13 09:40:38.914 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:38.914 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12816 bytes)
2025-06-13 09:40:39.172 [error] 'FuzzySymbolSearcher' Failed to read file tokens for cdecf0b0758f0d44f5c8c316fc9438dcddae0c4d38b7b082bd8e09c4f74df321: deleted
2025-06-13 09:40:41.436 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:41.436 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12914 bytes)
2025-06-13 09:40:56.085 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:56.086 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12914 bytes)
2025-06-13 09:40:56.326 [error] 'FuzzySymbolSearcher' Failed to read file tokens for ad3671a3b620c66506e1de1a351ca9ab44945aa5d9180bf29809242c7bf23751: deleted
2025-06-13 09:40:58.493 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:40:58.493 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (13533 bytes)
2025-06-13 09:43:20.386 [info] 'ViewTool' Tool called with path: package.json and view_range: [1,30]
2025-06-13 09:45:08.930 [info] 'ViewTool' Tool called with path: vite.config.ts and view_range: undefined
2025-06-13 09:45:15.421 [info] 'ViewTool' Tool called with path: client/src/main.tsx and view_range: undefined
2025-06-13 09:45:22.114 [info] 'ViewTool' Tool called with path: server/vite.ts and view_range: undefined
2025-06-13 09:45:28.898 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: undefined
2025-06-13 09:45:40.308 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:45:40.308 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2251 bytes)
2025-06-13 09:45:43.688 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:45:43.688 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2262 bytes)
2025-06-13 09:45:57.380 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:45:57.380 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2262 bytes)
2025-06-13 09:45:59.941 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:45:59.942 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2360 bytes)
2025-06-13 09:47:11.171 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:47:11.171 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2360 bytes)
2025-06-13 09:47:13.780 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:47:13.780 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2327 bytes)
2025-06-13 09:47:26.008 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:47:26.008 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2327 bytes)
2025-06-13 09:47:29.742 [info] 'ToolFileUtils' Reading file: server/vite.ts
2025-06-13 09:47:29.742 [info] 'ToolFileUtils' Successfully read file: server/vite.ts (2329 bytes)
2025-06-13 09:49:46.135 [info] 'ViewTool' Tool called with path: server/logs/quick-quote-submissions.txt and view_range: undefined
2025-06-13 09:49:57.991 [info] 'ToolFileUtils' Reading file: client/src/components/Footer.tsx
2025-06-13 09:49:58.672 [info] 'ToolFileUtils' Successfully read file: client/src/components/Footer.tsx (9390 bytes)
2025-06-13 09:50:01.630 [info] 'ToolFileUtils' Reading file: client/src/components/Footer.tsx
2025-06-13 09:50:01.630 [info] 'ToolFileUtils' Successfully read file: client/src/components/Footer.tsx (9452 bytes)
2025-06-13 09:50:15.797 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:50:16.501 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12489 bytes)
2025-06-13 09:50:19.396 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:50:19.396 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12698 bytes)
2025-06-13 09:50:31.175 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:50:31.175 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12698 bytes)
2025-06-13 09:50:31.407 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 937d363cfc57dc1f6033fee555fb102d7ddc039e98816a57104a901e9496acc5: deleted
2025-06-13 09:50:33.593 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:50:33.593 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12921 bytes)
2025-06-13 09:50:45.606 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:50:45.607 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (12921 bytes)
2025-06-13 09:50:45.844 [error] 'FuzzySymbolSearcher' Failed to read file tokens for f13e10d30bc2f2269336ab4bc03c0f4f6a5dd643d972fb9a8dd50c0102b648f4: deleted
2025-06-13 09:50:48.032 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:50:48.032 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (13396 bytes)
2025-06-13 09:51:40.440 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:51:40.440 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (13396 bytes)
2025-06-13 09:51:42.911 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:51:42.912 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (13907 bytes)
2025-06-13 09:51:56.854 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:51:56.855 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (13907 bytes)
2025-06-13 09:51:57.090 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 85d433de2ef4702768f1f698a7e6896cd5144a69c867345cfa3ccf551a2ab4b3: deleted
2025-06-13 09:51:59.303 [info] 'ToolFileUtils' Reading file: augment-guidelines.md
2025-06-13 09:51:59.304 [info] 'ToolFileUtils' Successfully read file: augment-guidelines.md (14604 bytes)
2025-06-13 10:04:38.964 [error] 'AugmentExtension' API request 05b5b1a5-e155-4d0f-af8c-cbecc58adfca to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 10:04:39.292 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 10:04:39.685 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 10:04:44.969 [error] 'AugmentExtension' API request bb9560e5-6f18-435c-8ac1-657942093afb to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 10:04:45.248 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 10:04:45.617 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 10:07:29.149 [info] 'AugmentExtension' Retrieving model config
2025-06-13 10:07:29.410 [info] 'AugmentExtension' Retrieved model config
2025-06-13 10:07:29.410 [info] 'AugmentExtension' Returning model config
2025-06-13 10:12:23.950 [error] 'AugmentExtension' API request 464a2412-593b-4420-9125-b46c3124c6c4 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 10:12:24.164 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 10:12:24.493 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 10:37:29.159 [info] 'AugmentExtension' Retrieving model config
2025-06-13 10:37:29.452 [info] 'AugmentExtension' Retrieved model config
2025-06-13 10:37:29.453 [info] 'AugmentExtension' Returning model config
2025-06-13 11:07:29.149 [info] 'AugmentExtension' Retrieving model config
2025-06-13 11:07:29.434 [info] 'AugmentExtension' Retrieved model config
2025-06-13 11:07:29.435 [info] 'AugmentExtension' Returning model config
2025-06-13 11:18:57.185 [error] 'AugmentExtension' API request 4b7855e4-e122-4c94-9afc-5d337de8e2a1 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:18:57.478 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:18:57.910 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:19:00.181 [error] 'AugmentExtension' API request 682277a9-3512-47dc-b824-27285a793b35 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:19:00.447 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:19:00.871 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:21:34.721 [error] 'AugmentExtension' API request a3fa94d6-2731-4ea9-b564-63c95dbf122e to https://i0.api.augmentcode.com/subscription-info failed: This operation was aborted
2025-06-13 11:21:34.983 [error] 'ChatApp' Failed to get subscription info: Error: This operation was aborted
2025-06-13 11:21:35.074 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":30492.443475,"timestamp":"2025-06-13T11:21:34.983Z"}]
2025-06-13 11:33:48.206 [error] 'AugmentExtension' API request b5f5b6a7-d716-41e1-b9c3-815a86c8af7d to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:33:48.469 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:33:48.863 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:36:51.220 [error] 'AugmentExtension' API request 206e4bfc-8dc9-45dd-bcfa-1aa12f2b844d to https://i0.api.augmentcode.com/find-missing response 502: Bad Gateway
2025-06-13 11:36:51.466 [info] 'DiskFileManager[workspace]' Operation failed with error Error: HTTP error: 502 Bad Gateway, retrying in 100 ms; retries = 0
2025-06-13 11:36:51.884 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:37:29.149 [info] 'AugmentExtension' Retrieving model config
2025-06-13 11:37:29.412 [info] 'AugmentExtension' Retrieved model config
2025-06-13 11:37:29.412 [info] 'AugmentExtension' Returning model config
2025-06-13 11:48:45.252 [error] 'AugmentExtension' API request ab87addc-75db-416a-886b-70060afd1a19 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:48:45.495 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:48:45.908 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:48:57.303 [error] 'AugmentExtension' API request 35ecb906-a843-4c14-8ef1-34106f9a6804 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:48:57.543 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:48:57.965 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:53:45.284 [error] 'AugmentExtension' API request ef406ad3-b140-4155-993b-7ff969523b98 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:53:45.565 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:53:45.987 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 11:54:36.295 [error] 'AugmentExtension' API request 505029c7-7e9a-4b76-9316-b540500f11b6 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 11:54:36.561 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 11:54:37.002 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 12:07:29.149 [info] 'AugmentExtension' Retrieving model config
2025-06-13 12:07:29.428 [info] 'AugmentExtension' Retrieved model config
2025-06-13 12:07:29.428 [info] 'AugmentExtension' Returning model config
2025-06-13 12:20:27.375 [error] 'AugmentExtension' API request 7862ec04-3ff5-4102-aa64-29d20a33368a to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 12:20:27.623 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 12:20:28.081 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 12:37:29.149 [info] 'AugmentExtension' Retrieving model config
2025-06-13 12:37:29.385 [info] 'AugmentExtension' Retrieved model config
2025-06-13 12:37:29.385 [info] 'AugmentExtension' Returning model config
2025-06-13 12:38:54.649 [error] 'AugmentExtension' API request 3dffdf16-c80b-4126-9b9c-57fb0f53e7dc to https://i0.api.augmentcode.com/batch-upload failed: This operation was aborted
2025-06-13 12:38:54.857 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 12:38:55.323 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
2025-06-13 12:53:48.423 [error] 'AugmentExtension' API request f29ed03c-ab7b-481e-88c5-a6f7edddb1c1 to https://i0.api.augmentcode.com/find-missing failed: This operation was aborted
2025-06-13 12:53:48.632 [info] 'DiskFileManager[workspace]' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-06-13 12:53:49.008 [info] 'DiskFileManager[workspace]' Operation succeeded after 1 transient failures
